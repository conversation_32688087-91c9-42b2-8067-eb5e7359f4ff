"""
Excel file generation module.
Handles creation and formatting of Excel output files.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
import sys
from pathlib import Path

# Ensure src directory is in path
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from utils.lazy_imports import get_pandas
from config.constants import VALIDATION_LISTS


class ExcelGenerator:
    """Handles Excel file generation and formatting."""
    
    def __init__(self):
        """Initialize the Excel generator."""
        self.logger = logging.getLogger(__name__)
    
    def generate_excel_file(self,
                          moai_data: Dict[str, Any],
                          plan_df: 'pd.DataFrame',
                          project_info: Dict[str, str],
                          output_path: str) -> bool:
        """
        Generate the complete Excel file with all sheets.

        Args:
            moai_data: Processed MOAI data
            plan_df: Processed Plan Adressage DataFrame
            project_info: Project information (commune, insee, id_tache)
            output_path: Path to save the Excel file

        Returns:
            True if successful, False otherwise
        """
        try:
            pd = get_pandas()

            # Ensure output directory exists
            from utils.file_utils import ensure_directory_exists
            import os
            output_dir = os.path.dirname(output_path)
            if output_dir and not ensure_directory_exists(output_dir):
                raise Exception(f"Cannot create output directory: {output_dir}")

            # Create the three main DataFrames
            df_cm = self._create_cm_adresse_sheet(moai_data, project_info)
            df_plan = self._create_plan_adressage_sheet(plan_df, project_info)
            df_commune = self._create_commune_info_sheet(project_info, df_cm)

            # Generate sheet names
            sheet_names = self._generate_sheet_names(project_info['id_tache'])

            # Write to Excel with formatting
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Write the main sheets
                df_cm.to_excel(writer, sheet_name=sheet_names['cm'], index=False)
                df_plan.to_excel(writer, sheet_name=sheet_names['plan'], index=False)
                df_commune.to_excel(writer, sheet_name=sheet_names['commune'], index=False)

                # Apply styling to all sheets
                self._apply_sheet_styling(writer, sheet_names['cm'])
                self._apply_sheet_styling(writer, sheet_names['plan'])
                self._apply_sheet_styling(writer, sheet_names['commune'])

                # Add data validations
                self._add_data_validations(writer, df_cm, sheet_names['cm'])
                self._create_validation_sheet(writer)
                self._add_duration_formula(writer, len(df_cm), df_plan,
                                         sheet_names['cm'], sheet_names['plan'], sheet_names['commune'])
                self._add_commune_validations(writer, sheet_names['commune'])
                self._add_plan_adressage_validations(writer, df_plan, sheet_names['plan'])

            self.logger.info(f"Excel file generated successfully: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error generating Excel file: {e}")
            return False
    
    def _create_cm_adresse_sheet(self, moai_data: Dict[str, Any], project_info: Dict[str, str]) -> 'pd.DataFrame':
        """
        Create the CM Adresse sheet DataFrame.

        Args:
            moai_data: Processed MOAI data
            project_info: Project information

        Returns:
            CM Adresse DataFrame
        """
        pd = get_pandas()

        date_affectation = datetime.now().strftime("%d/%m/%Y")
        num_rows = moai_data['num_rows']

        # Use communes from MOAI if available, otherwise use project commune
        communes = (moai_data['communes_moai'] if len(moai_data['communes_moai']) > 0
                   else [project_info['nom_commune']] * num_rows)

        df_cm = pd.DataFrame({
            'Nom commune': [project_info['nom_commune']] * num_rows,  # Colonne A
            'Insee': [project_info['insee']] * num_rows,              # Colonne B
            'ID Tache': moai_data['id_taches'],                       # Colonne C
            'Voie demandé': moai_data['voies_demandees'],             # Colonne D
            'Motif Voie': [''] * num_rows,                            # Colonne E
            'CODE RIVOLI': [''] * num_rows,                           # Colonne F
            'GPS (X,Y)': [''] * num_rows,                             # Colonne G
            'Centre/Zone': [''] * num_rows,                           # Colonne H
            'Status PC': [''] * num_rows,                             # Colonne I
            'Descriptif Commentaire': [''] * num_rows,                # Colonne J
            'Collaborateur': [''] * num_rows,                         # Colonne K
            'Date affectation': [date_affectation] * num_rows,        # Colonne L
            'Date traitement': [''] * num_rows,                       # Colonne M
            'Date livraison': [''] * num_rows,                        # Colonne N
            'Durée': [''] * num_rows,                                 # Colonne O
            'STATUT Ticket': [''] * num_rows                          # Colonne P
        })

        return df_cm

    def _create_plan_adressage_sheet(self, plan_df: 'pd.DataFrame', project_info: Dict[str, str]) -> 'pd.DataFrame':
        """
        Create the Plan Adressage sheet DataFrame with additional commune info columns.

        Args:
            plan_df: Original Plan Adressage DataFrame
            project_info: Project information

        Returns:
            Modified Plan Adressage DataFrame
        """
        pd = get_pandas()

        # Create a copy of the original DataFrame
        df_plan = plan_df.copy()

        # Add commune info columns at the beginning
        num_rows = len(df_plan)
        df_plan.insert(0, 'Insee', [project_info['insee']] * num_rows)
        df_plan.insert(0, 'Nom commune', [project_info['nom_commune']] * num_rows)

        return df_plan

    def _create_commune_info_sheet(self, project_info: Dict[str, str], df_cm: 'pd.DataFrame') -> 'pd.DataFrame':
        """
        Create the commune information sheet DataFrame.

        Args:
            project_info: Project information
            df_cm: CM Adresse DataFrame to extract data from

        Returns:
            Commune information DataFrame
        """
        pd = get_pandas()

        date_affectation = datetime.now().strftime("%d/%m/%Y")

        df_commune = pd.DataFrame({
            'Nom de commune': [project_info['nom_commune']],           # Colonne A
            'ID tâche Plan Adressage': [project_info['id_tache']],     # Colonne B
            'Code INSEE': [project_info['insee']],                     # Colonne C
            'Nbr des voies CM': [''],                                 # Colonne D - Will be calculated automatically
            'Nbr des IMB PA': [''],                                   # Colonne E - Will be calculated automatically
            'Date d\'affectation': [date_affectation],                # Colonne F
            'Temps préparation QGis': [0],                            # Colonne G - Nouvelle colonne
            'Durée Totale CM': [5],                                   # Colonne H - Initialized to 5 min
            'Duréé Totale PA': [0],                                   # Colonne I - Initialized to 0
            'Traitement Optimum': [0],                                # Colonne J - Nouvelle colonne
            'Durée Finale': [''],                                     # Colonne K - Will be calculated with Excel formula
            'Date Livraison': [''],                                   # Colonne L
            'Etat Ticket PA ': [''],                                  # Colonne M
            'ID Tache 501/511': [''],                                 # Colonne N
            'Date Dépose Ticket 501/511': [''],                       # Colonne O
            'Dépose Ticket UPR': ['Non Créé'],                       # Colonne P
            'ID tâche UPR': [''],                                     # Colonne Q
            'Collaborateur': ['']                                     # Colonne R
        })

        return df_commune

    def _apply_sheet_styling(self, writer, sheet_name: str):
        """
        Apply default styling to a sheet: center alignment, freeze first row, blue header.

        Args:
            writer: Excel writer object
            sheet_name: Name of the sheet to style
        """
        try:
            from openpyxl.styles import Alignment, PatternFill, Font

            worksheet = writer.sheets[sheet_name]

            # Freeze the first row
            worksheet.freeze_panes = 'A2'

            # Define styles
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')  # Blue froid
            header_font = Font(color='FFFFFF', bold=True)  # White text, bold

            # Apply center alignment to all cells
            for row in worksheet.iter_rows():
                for cell in row:
                    cell.alignment = center_alignment

            # Apply header styling to first row
            for cell in worksheet[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = center_alignment

            # Apply date formatting to date columns
            self._apply_date_formatting(worksheet, sheet_name)

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 30)  # Max width of 30
                worksheet.column_dimensions[column_letter].width = adjusted_width

            self.logger.info(f"Styling applied to sheet: {sheet_name}")

        except Exception as e:
            self.logger.error(f"Error applying styling to sheet {sheet_name}: {e}")

    def _apply_date_formatting(self, worksheet, sheet_name: str):
        """Apply date formatting to date columns (format as date only, no time)."""
        try:
            # Define specific date columns for each sheet type (by position)
            date_columns = {
                'CM Adresse': ['L', 'M', 'N'],  # Date affectation, Date traitement, Date livraison
                'Plan Adressage': [],  # No date columns in Plan Adressage sheet
                'Suivi Tickets': ['F', 'L', 'O']  # Date d'affectation, Date Livraison, Date Dépose Ticket 501/511
            }

            # Get the appropriate date columns for this sheet
            sheet_date_columns = []
            for sheet_type, columns in date_columns.items():
                if sheet_type in sheet_name:
                    sheet_date_columns = columns
                    break

            # If no predefined columns, use intelligent detection based on header names
            if not sheet_date_columns:
                sheet_date_columns = self._detect_date_columns_by_header(worksheet)

            # Apply date formatting to identified columns
            if sheet_date_columns:
                for col_letter in sheet_date_columns:
                    # Apply to all rows in the column (starting from row 2 to skip header)
                    for row in range(2, worksheet.max_row + 1):
                        cell = worksheet[f'{col_letter}{row}']
                        if cell.value is not None and str(cell.value).strip() != '':
                            # Set date format (dd/mm/yyyy)
                            cell.number_format = 'DD/MM/YYYY'

                self.logger.info(f"Date formatting applied to columns {sheet_date_columns} in sheet: {sheet_name}")

        except Exception as e:
            self.logger.error(f"Error applying date formatting to sheet {sheet_name}: {e}")

    def _detect_date_columns_by_header(self, worksheet):
        """Detect date columns by analyzing header names, excluding duration columns."""
        date_columns = []

        try:
            # Check each column header
            for col in range(1, worksheet.max_column + 1):
                header_cell = worksheet.cell(row=1, column=col)
                if header_cell.value:
                    header_text = str(header_cell.value).lower()

                    # Exclude duration/time columns first
                    duration_keywords = ['durée', 'duration', 'temps', 'time', 'traitement optimum', 'finale', 'motif']
                    is_duration = any(keyword in header_text for keyword in duration_keywords)

                    # Check for date keywords only if it's not a duration column
                    if not is_duration:
                        date_keywords = ['date', 'livraison', 'affectation', 'dépose', 'traitement']
                        is_date = any(keyword in header_text for keyword in date_keywords)

                        if is_date:
                            col_letter = self._get_column_letter(col)
                            date_columns.append(col_letter)

            return date_columns

        except Exception as e:
            self.logger.error(f"Error detecting date columns: {e}")
            return []

    def _generate_sheet_names(self, id_tache: str) -> Dict[str, str]:
        """
        Generate sheet names based on task ID.
        
        Args:
            id_tache: Task ID
            
        Returns:
            Dictionary with sheet names
        """
        id_tache_clean = id_tache.replace(" ", "").replace("/", "-").replace("\\", "-")
        
        return {
            'cm': f"{id_tache_clean}-CM Adresse",
            'plan': f"{id_tache_clean}-Plan Adressage",
            'commune': f"{id_tache_clean}-Informations Commune"
        }
    
    def _add_data_validations(self, writer, df_cm: 'pd.DataFrame', sheet_name: str):
        """Add data validation lists to CM Adresse sheet."""
        try:
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]

            from openpyxl.worksheet.datavalidation import DataValidation

            # Get column indices for validation
            columns = df_cm.columns.tolist()

            # Add validations for specific columns (same mappings, positions will be automatically adjusted)
            validation_mappings = {
                'Domaine': 'Domaine',
                'Type de Commune': 'Type de Commune',
                'Type de base': 'Type de base',
                'Motif Voie': 'Motif Voie',
                'Status PC': 'PC Status',
                'Descriptif Commentaire': 'XY Status',
                'Collaborateur': 'Collaborateur',
                'STATUT Ticket': 'STATUT Ticket'
            }

            for col_name, validation_key in validation_mappings.items():
                if col_name in columns:
                    col_idx = columns.index(col_name) + 1  # Excel is 1-indexed
                    col_letter = self._get_column_letter(col_idx)

                    # Create validation list
                    validation_list = VALIDATION_LISTS.get(validation_key, [])
                    if validation_list:
                        dv = DataValidation(
                            type="list",
                            formula1=f'"{",".join(validation_list)}"',
                            allow_blank=True
                        )
                        dv.error = f'Valeur non valide pour {col_name}'
                        dv.errorTitle = 'Erreur de validation'

                        # Apply to all data rows (skip header)
                        range_str = f"{col_letter}2:{col_letter}{len(df_cm) + 1}"
                        dv.add(range_str)
                        worksheet.add_data_validation(dv)

            self.logger.info("Data validations added to CM Adresse sheet")

        except Exception as e:
            self.logger.error(f"Error adding data validations: {e}")
    
    def _create_validation_sheet(self, writer):
        """Create a hidden sheet with validation lists."""
        try:
            pd = get_pandas()
            
            # Create DataFrame with all validation lists
            max_length = max(len(lst) for lst in VALIDATION_LISTS.values())
            validation_data = {}
            
            for key, values in VALIDATION_LISTS.items():
                # Pad shorter lists with empty strings
                padded_values = values + [''] * (max_length - len(values))
                validation_data[key] = padded_values
            
            df_validation = pd.DataFrame(validation_data)
            df_validation.to_excel(writer, sheet_name='ValidationLists', index=False)
            
            # Hide the validation sheet
            if 'ValidationLists' in writer.sheets:
                writer.sheets['ValidationLists'].sheet_state = 'hidden'
            
            self.logger.info("Validation sheet created")
            
        except Exception as e:
            self.logger.error(f"Error creating validation sheet: {e}")
    
    def _get_column_letter(self, col_num: int) -> str:
        """Convert column number to Excel column letter."""
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def _add_duration_formula(self, writer, cm_rows: int, plan_df: 'pd.DataFrame',
                            cm_sheet: str, plan_sheet: str, commune_sheet: str):
        """Add duration calculation formulas."""
        try:
            workbook = writer.book
            commune_ws = writer.sheets[commune_sheet]

            # Add formula to calculate total CM duration (sum of CM sheet duration column)
            # 'Durée' is now column O (15th column) in CM sheet after removing columns
            cm_duration_formula = f"=SUM('{cm_sheet}'!O2:O{cm_rows + 1})+5"
            commune_ws['H2'] = cm_duration_formula  # Durée Totale CM (column H)

            # Add formula to count unique voies in CM sheet (count non-empty rows)
            voies_count_formula = f"=COUNTA('{cm_sheet}'!D2:D{cm_rows + 1})"  # Voie demandé column (now column D)
            commune_ws['D2'] = voies_count_formula  # Nbr des voies CM (column D)

            # Add formula to count unique IMB in Plan Adressage (excluding empty and duplicates)
            plan_rows = len(plan_df)
            if plan_rows > 0:
                # Count unique non-empty values in column C of Plan Adressage sheet (IMB codes like IMB/87193/X/0015)
                # This formula counts each unique IMB only once, excluding duplicates and empty cells
                # Example: 52 IMB with 2 duplicates = 50 unique IMB
                imb_count_formula = f"=SUMPRODUCT(('{plan_sheet}'!C2:C{plan_rows + 1}<>\"\")*1/COUNTIF('{plan_sheet}'!C2:C{plan_rows + 1},'{plan_sheet}'!C2:C{plan_rows + 1}&\"\"))"
                commune_ws['E2'] = imb_count_formula  # Nbr des IMB PA (column E)

            # Add formula to calculate total PA duration (sum of PA sheet duration column)
            if plan_rows > 0:
                # Find the 'Durée' column in plan sheet (should be the last column after adding commune/insee)
                plan_columns = plan_df.columns.tolist()
                if 'Durée' in plan_columns:
                    duree_col_idx = plan_columns.index('Durée') +1  
                    duree_col_letter = self._get_column_letter(duree_col_idx)
                    pa_duration_formula = f"=SUM('{plan_sheet}'!{duree_col_letter}2:{duree_col_letter}{plan_rows + 1})"
                    commune_ws['I2'] = pa_duration_formula  # Durée Totale PA (column I)

            # Add formula for final duration (sum of CM, PA durations, QGis preparation time and Traitement Optimum)
            commune_ws['K2'] = "=G2+H2+I2+J2"  # Durée Finale = Temps préparation QGis + Durée Totale CM + Durée Totale PA + Traitement Optimum

            self.logger.info("Duration formulas added")

        except Exception as e:
            self.logger.error(f"Error adding duration formulas: {e}")

    def _add_commune_validations(self, writer, sheet_name: str):
        """Add data validations to commune information sheet."""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            worksheet = writer.sheets[sheet_name]

            # Add validation for 'Etat Ticket PA' (now column M)
            etat_validation = DataValidation(
                type="list",
                formula1=f'"{",".join(VALIDATION_LISTS["Etat"])}"',
                allow_blank=True
            )
            etat_validation.add('M2')
            worksheet.add_data_validation(etat_validation)

            # Add validation for 'Dépose Ticket UPR' (now column P)
            upr_validation = DataValidation(
                type="list",
                formula1=f'"{",".join(VALIDATION_LISTS["Depose Ticket UPR"])}"',
                allow_blank=True
            )
            upr_validation.add('P2')
            worksheet.add_data_validation(upr_validation)

            # Add validation for 'Collaborateur' (now column R)
            collab_validation = DataValidation(
                type="list",
                formula1=f'"{",".join(VALIDATION_LISTS["Collaborateur"])}"',
                allow_blank=True
            )
            collab_validation.add('R2')
            worksheet.add_data_validation(collab_validation)

            self.logger.info("Commune validations added")

        except Exception as e:
            self.logger.error(f"Error adding commune validations: {e}")

    def _add_plan_adressage_validations(self, writer, plan_df: 'pd.DataFrame', sheet_name: str):
        """Add data validations to Plan Adressage sheet."""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            worksheet = writer.sheets[sheet_name]
            columns = plan_df.columns.tolist()

            # Add validation for 'Collaborateur' column if it exists
            if 'Collaborateur' in columns:
                col_idx = columns.index('Collaborateur') + 1
                col_letter = self._get_column_letter(col_idx)

                collab_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(VALIDATION_LISTS["Collaborateur"])}"',
                    allow_blank=True
                )

                # Apply to all data rows
                range_str = f"{col_letter}2:{col_letter}{len(plan_df) + 1}"
                collab_validation.add(range_str)
                worksheet.add_data_validation(collab_validation)

            self.logger.info("Plan Adressage validations added")

        except Exception as e:
            self.logger.error(f"Error adding Plan Adressage validations: {e}")

    def generate_filename(self, nom_commune: str, id_tache: str, insee: str) -> str:
        """
        Generate standardized filename for the Excel output.

        Args:
            nom_commune: Commune name
            id_tache: Task ID
            insee: INSEE code

        Returns:
            Generated filename
        """
        return f"Suivi_{nom_commune}_{id_tache}_{insee}.xlsx"
