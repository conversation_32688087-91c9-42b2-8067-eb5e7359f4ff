2025-06-08 01:32:14 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 01:32:14 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 01:32:14 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 01:32:14 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 01:32:14 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 01:32:14 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 01:32:14 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-08 01:32:14 - __main__ - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 01:32:14 - __main__ - INFO - setup_application:47 - Version: 2.1
2025-06-08 01:32:14 - __main__ - INFO - setup_application:48 - Author: Equipe BLI
2025-06-08 01:32:14 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-08 01:32:14 - __main__ - INFO - main:99 - Creating application...
2025-06-08 01:32:15 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 01:32:15 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 01:32:16 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 01:32:16 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 01:32:16 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 01:32:16 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 01:32:16 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 01:32:16 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 01:32:16 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-08 01:32:16 - __main__ - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 01:32:16 - __main__ - INFO - setup_application:47 - Version: 2.1
2025-06-08 01:32:16 - __main__ - INFO - setup_application:48 - Author: Equipe BLI
2025-06-08 01:32:16 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-08 01:32:16 - __main__ - INFO - main:99 - Creating application...
2025-06-08 01:32:16 - ui.navigation - INFO - register_module:188 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 01:32:16 - ui.navigation - INFO - register_module:188 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 01:32:16 - ui.navigation - INFO - register_module:188 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 01:32:16 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:32:16 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 01:32:16 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 01:32:16 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 01:32:17 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:32:17 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 01:32:17 - __main__ - INFO - main:102 - Application created successfully
2025-06-08 01:32:17 - __main__ - INFO - main:103 - Starting main loop...
2025-06-08 01:32:17 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 01:32:17 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 01:32:17 - ui.navigation - INFO - register_module:188 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 01:32:17 - ui.navigation - INFO - register_module:188 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 01:32:17 - ui.navigation - INFO - register_module:188 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 01:32:17 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:32:17 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 01:32:18 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:32:18 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 01:32:18 - __main__ - INFO - main:102 - Application created successfully
2025-06-08 01:32:18 - __main__ - INFO - main:103 - Starting main loop...
2025-06-08 01:32:18 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 01:32:18 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 01:32:25 - ui.home_screen - INFO - _open_teams_folder_directly:372 - User clicked Teams folder button - opening directly
2025-06-08 01:32:26 - ui.home_screen - INFO - _open_teams_folder_directly:381 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 01:33:39 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: about
2025-06-08 01:33:39 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: about
2025-06-08 01:33:40 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:33:40 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:33:42 - ui.home_screen - INFO - _open_teams_folder_directly:372 - User clicked Teams folder button - opening directly
2025-06-08 01:33:44 - ui.home_screen - INFO - _open_teams_folder_directly:381 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 01:33:44 - ui.home_screen - INFO - _open_teams_folder_directly:372 - User clicked Teams folder button - opening directly
2025-06-08 01:33:45 - ui.home_screen - INFO - _open_teams_folder_directly:381 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 01:33:51 - ui.home_screen - INFO - _open_suivi_global:337 - User clicked Suivi Global Tickets button
2025-06-08 01:33:51 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_global
2025-06-08 01:33:51 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 01:33:51 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:33:51 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 01:33:51 - ui.navigation - INFO - _load_module:292 - Module suivi_global created and loaded successfully
2025-06-08 01:33:51 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_global
2025-06-08 01:33:51 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-08 01:33:53 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:33:54 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:33:57 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:33:57 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:34:00 - ui.home_screen - INFO - _open_suivi_global:337 - User clicked Suivi Global Tickets button
2025-06-08 01:34:00 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_global
2025-06-08 01:34:00 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 01:34:00 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:34:00 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 01:34:00 - ui.navigation - INFO - _load_module:292 - Module suivi_global created and loaded successfully
2025-06-08 01:34:00 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_global
2025-06-08 01:34:00 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:34:03 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:34:05 - ui.modules.suivi_global_module - INFO - _reset_module:328 - Module reset successfully
2025-06-08 01:34:06 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:34:07 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:34:08 - ui.home_screen - INFO - _open_team_stats:352 - User clicked Team Statistics button
2025-06-08 01:34:08 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 01:34:08 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 01:34:08 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:34:08 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 01:34:08 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 01:34:08 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 01:34:08 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 01:34:10 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 01:34:16 - ui.modules.team_stats_module - INFO - _reset_module:344 - Module reset successfully
2025-06-08 01:34:18 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:34:18 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:34:19 - ui.home_screen - INFO - _open_suivi_generator:322 - User clicked Suivi Generator button
2025-06-08 01:34:19 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:34:19 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:34:19 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:34:20 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:34:20 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:34:20 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-08 01:34:20 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:34:23 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-08 01:34:23 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-08 01:34:23 - ui.components.generation - INFO - reset:208 - Generation section reset
2025-06-08 01:34:23 - ui.modules.suivi_generator_module - ERROR - _reset_module:521 - Error resetting module: 'NoneType' object has no attribute 'clear_session'
2025-06-08 01:34:27 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: about
2025-06-08 01:34:28 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: about
