"""
Script de paquetage pour SofreTrack Pro v3.0
Crée un exécutable portable avec PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# Configuration
APP_NAME = "SofreTrack Pro"
APP_VERSION = "3.0"
MAIN_SCRIPT = "../src/main.py"
ICON_ICO = "../Icone_App_Sharp.ico"
DIST_DIR = "dist"
BUILD_DIR = "build"

def clean_previous_builds():
    """Nettoie les builds précédents"""
    print("Nettoyage des builds precedents...")

    try:
        dirs_to_clean = [DIST_DIR, BUILD_DIR, "__pycache__"]
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                try:
                    shutil.rmtree(dir_name)
                    print(f"   Supprime: {dir_name}")
                except Exception as e:
                    print(f"   Erreur suppression {dir_name}: {e}")

        # Supprimer les fichiers .spec
        try:
            for spec_file in Path(".").glob("*.spec"):
                spec_file.unlink()
                print(f"   Supprime: {spec_file}")
        except Exception as e:
            print(f"   Erreur suppression .spec: {e}")

        print("   Nettoyage termine")
        return True

    except Exception as e:
        print(f"   Erreur generale nettoyage: {e}")
        return True  # Continue même si nettoyage échoue

def install_dependencies():
    """Installe les dépendances nécessaires"""
    print("Installation des dependances...")

    try:
        # Installer PyInstaller en premier si pas présent
        try:
            import pyinstaller
        except ImportError:
            print("   Installation de PyInstaller...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller>=6.0.0"],
                          check=True, capture_output=True, text=True)

        # Installer les autres dépendances
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True, capture_output=True, text=True)
        print("   Dependances installees avec succes")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   Erreur lors de l'installation: {e}")
        print(f"   Details: {e.stderr if hasattr(e, 'stderr') else 'Aucun detail'}")
        return False
    except Exception as e:
        print(f"   Erreur inattendue: {e}")
        return False

def build_executable_simple():
    """Compile l'application avec PyInstaller"""
    print("Compilation avec PyInstaller...")

    try:
        # Vérifier que les fichiers nécessaires existent
        required_files = [
            MAIN_SCRIPT,
            ICON_ICO,
            "../Icone_App.png",
            "../logo_Sofrecom.png"
        ]
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"   ERREUR: Fichier manquant: {file_path}")
                return False

        # Commande PyInstaller avec ressources incluses
        cmd = [
            "pyinstaller",
            "--onefile",                    # Un seul fichier exécutable
            "--windowed",                   # Pas de console
            "--clean",                      # Nettoyer avant build
            "--noconfirm",                  # Pas de confirmation
            f"--name={APP_NAME}",          # Nom de l'exécutable
            f"--icon={ICON_ICO}",          # Icône principale
            # Inclure les ressources dans l'exécutable
            "--add-data", "../Icone_App.png;.",
            "--add-data", "../Icone_App_Sharp.ico;.",
            "--add-data", "../logo_Sofrecom.png;.",
            # Imports cachés essentiels
            "--hidden-import=pandas",
            "--hidden-import=openpyxl",
            "--hidden-import=PIL",
            "--hidden-import=PIL.Image",
            "--hidden-import=PIL.ImageTk",
            "--hidden-import=tkinter",
            "--hidden-import=getpass",
            "--hidden-import=subprocess",
            MAIN_SCRIPT
        ]

        print("   Commande PyInstaller:")
        print(f"   {' '.join(cmd)}")
        print()

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("   Compilation reussie!")

            # Renommer l'exécutable si nécessaire
            exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"
            if not exe_path.exists():
                # Chercher l'exe avec un nom différent
                for exe_file in Path(DIST_DIR).glob("*.exe"):
                    exe_file.rename(exe_path)
                    print(f"   Executable renomme: {exe_path}")
                    break

            return True
        else:
            print(f"   Erreur de compilation:")
            print(f"   STDOUT: {result.stdout}")
            print(f"   STDERR: {result.stderr}")
            return False

    except FileNotFoundError:
        print("   PyInstaller non trouve. Installez-le avec: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"   Erreur inattendue: {e}")
        return False

def test_executable():
    """Teste si l'exécutable fonctionne"""
    exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"

    if not exe_path.exists():
        print("   Executable non trouve")
        return False

    print(f"   Executable cree: {exe_path}")
    print(f"   Taille: {exe_path.stat().st_size / (1024*1024):.1f} MB")

    # Test rapide (lancement et fermeture immédiate)
    print("   Test de l'executable...")
    try:
        # Lancer l'exe et le fermer après 2 secondes
        import time

        process = subprocess.Popen([str(exe_path)])
        time.sleep(2)  # Laisser le temps de démarrer
        process.terminate()
        process.wait(timeout=5)

        print("   Test reussi - L'executable se lance correctement")
        return True

    except Exception as e:
        print(f"   Test non concluant: {e}")
        print("   Testez manuellement l'executable")
        return True  # On considère que c'est OK

def create_portable_package():
    """Crée un package portable simple"""
    print("Creation du package portable...")

    try:
        exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"
        if not exe_path.exists():
            print("   Executable non trouve")
            return False

        # Créer un dossier portable
        portable_dir = Path(DIST_DIR) / "SofreTrack_Pro_Portable"
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        portable_dir.mkdir(parents=True)

        # Copier l'exécutable
        shutil.copy2(exe_path, portable_dir / f"{APP_NAME}.exe")

        # Créer un README simple
        readme_content = f"""SofreTrack Pro v{APP_VERSION} - Version Portable

INSTALLATION:
- Aucune installation requise
- Double-cliquez sur "SofreTrack Pro.exe" pour lancer

FONCTIONNALITES:
- Generateur de Suivi Production
- Suivi Global des Tickets
- Statistiques Equipe avec exports Excel
- Acces direct au Canal Teams

COMPATIBILITE:
- Windows 10/11 (64-bit)
- Fonctionne sans droits administrateur

Developpe par l'Equipe B1L Sofrecom Tunisie
© 2024 Sofrecom - Tous droits reserves
"""

        with open(portable_dir / "README.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)

        print(f"   Package portable cree: {portable_dir}")
        return True

    except Exception as e:
        print(f"   Erreur creation package: {e}")
        return False

def main():
    """Fonction principale de build"""
    print("BUILD SOFRETRACK PRO - VERSION PORTABLE")
    print(f"Application: {APP_NAME} v{APP_VERSION}")
    print("Architecture: Nouvelle génération avec interface Sofrecom")
    print("=" * 60)

    # Étapes de build mises à jour
    steps = [
        ("Nettoyage", clean_previous_builds),
        ("Installation des dependances", install_dependencies),
        ("Compilation", build_executable_simple),
        ("Test", test_executable),
        ("Package portable", create_portable_package)
    ]

    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"Echec a l'etape: {step_name}")
            return False

    # Résumé final
    exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"
    portable_dir = Path(DIST_DIR) / "SofreTrack_Pro_Portable"

    if exe_path.exists():
        print("\n" + "=" * 60)
        print("BUILD SOFRETRACK PRO REUSSI!")
        print("=" * 60)
        print(f"Executable: {exe_path}")
        print(f"Taille: {exe_path.stat().st_size / (1024*1024):.1f} MB")

        if portable_dir.exists():
            print(f"Package portable: {portable_dir}")
            print(f"Contenu: {len(list(portable_dir.iterdir()))} fichiers")

        print("\nCOMPATIBILITE MULTI-PC:")
        print("✅ Windows 10/11 (64-bit)")
        print("✅ Aucune installation requise")
        print("✅ Fonctionne sans droits admin")
        print("✅ Username dynamique pour Teams")
        print("✅ Interface Sofrecom moderne")

        print("\nDISTRIBUTION:")
        print("- Copiez le dossier 'SofreTrack_Pro_Portable' sur d'autres PC")
        print("- Ou distribuez directement l'exécutable")
        print("- Double-clic pour lancer l'application")

        return True
    else:
        print("\nEchec: Executable non trouve")
        return False

if __name__ == "__main__":
    main()
