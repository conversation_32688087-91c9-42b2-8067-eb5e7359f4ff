"""
Suivi Global Tickets module - Aggregate commune suivi files into global Excel.
"""

import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import logging
import os
from typing import Optional, List, Dict, Any
from pathlib import Path
import sys

# Ensure src directory is in path
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from config.constants import COLORS, UIConfig
from core import FileProcessor, DataValidator, ExcelGenerator
from utils.file_utils import get_icon_path
from utils.lazy_imports import get_pandas
from utils.performance import run_async_task
from utils.session_manager import SessionManager, AutoSaveManager
from ui.styles import StyleManager, create_card_frame, create_section_header
from ui.keyboard_shortcuts import KeyboardShortcutManager

logger = logging.getLogger(__name__)


class SuiviGlobalModule:
    """Suivi Global Tickets module for aggregating commune suivi files."""
    
    def __init__(self, parent: tk.Widget, navigation_manager=None):
        """
        Initialize the Suivi Global module.

        Args:
            parent: Parent widget
            navigation_manager: Navigation manager instance
        """
        self.parent = parent
        self.navigation_manager = navigation_manager
        self.logger = logging.getLogger(__name__)

        # Initialize core components
        try:
            self.file_processor = FileProcessor()
            self.data_validator = DataValidator()
            self.excel_generator = ExcelGenerator()
            self.logger.info("Core components initialized")
        except Exception as e:
            self.logger.error(f"Error initializing core components: {e}")
            self.file_processor = None
            self.data_validator = None
            self.excel_generator = None

        # Module data
        self.commune_folders = []
        self.processed_data = []
        self.existing_communes = {}  # Store existing commune data for comparison
        self.new_communes = []  # Track new communes found during scan
        self.updated_communes = []  # Track communes that will be updated
        # Get dynamic Teams path for current user
        from config.constants import TeamsConfig
        self.teams_folder_path = TeamsConfig.get_global_teams_path()
        self.global_excel_filename = "Suivis Global Tickets CMS Adr_PA.xlsx"

        # UI components
        self.progress_var = None
        self.progress_bar = None
        self.status_label = None
        self.generate_button = None
        self.summary_text = None
        self.scan_button = None

        # Session management (optional)
        self.session_manager = None
        self.auto_save_manager = None
        self.keyboard_manager = None

        # Create UI first
        self._create_module_ui()

        # Initialize optional features after UI is created
        self.parent.after(100, self._initialize_optional_features)

    def _initialize_optional_features(self):
        """Initialize optional features after UI is ready."""
        try:
            # Session management
            self.session_manager = SessionManager("sessions/suivi_global")
            self.auto_save_manager = AutoSaveManager(self.session_manager)

            # Setup keyboard shortcuts
            if self.navigation_manager and hasattr(self.navigation_manager, 'root'):
                self.keyboard_manager = KeyboardShortcutManager(self.navigation_manager.root)
                self._setup_module_shortcuts()

            # Restore session
            self._restore_session()

            # Start auto-save
            if self.auto_save_manager and self.navigation_manager and hasattr(self.navigation_manager, 'root'):
                self.auto_save_manager.start_auto_save(self.navigation_manager.root)

            self.logger.info("Optional features initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing optional features: {e}")

    def _create_module_ui(self):
        """Create the module user interface."""
        try:
            # Module title bar
            self._create_title_bar()

            # Main content area
            self._create_content_area()

            self.logger.info("Module UI created successfully")

        except Exception as e:
            self.logger.error(f"Error creating module UI: {e}")
            self._create_error_ui(str(e))

    def _create_error_ui(self, error_message: str):
        """Create a simple error display when module fails to load."""
        error_frame = tk.Frame(self.parent, bg=COLORS['BG'])
        error_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Error icon and message
        error_label = tk.Label(
            error_frame,
            text=f"❌ Erreur lors du chargement du module:\n\n{error_message}",
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['DANGER'],
            bg=COLORS['BG'],
            justify=tk.CENTER
        )
        error_label.pack(expand=True)

        # Retry button
        retry_btn = tk.Button(
            error_frame,
            text="🔄 Réessayer",
            command=self._retry_initialization,
            bg=COLORS['PRIMARY'],
            fg='white',
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=20,
            pady=10
        )
        retry_btn.pack(pady=10)

    def _retry_initialization(self):
        """Retry module initialization."""
        try:
            # Clear the parent
            for widget in self.parent.winfo_children():
                widget.destroy()

            # Reinitialize
            self.__init__(self.parent, self.navigation_manager)

        except Exception as e:
            self.logger.error(f"Retry failed: {e}")
            messagebox.showerror("Erreur", f"Impossible de réinitialiser le module:\n{e}")

    def _create_title_bar(self):
        """Create the module title bar."""
        title_frame = tk.Frame(self.parent, bg=COLORS['CARD'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        title_frame.pack_propagate(False)
        
        # Title content
        title_content = tk.Frame(title_frame, bg=COLORS['CARD'])
        title_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # Module icon and title
        title_left = tk.Frame(title_content, bg=COLORS['CARD'])
        title_left.pack(side=tk.LEFT, fill=tk.Y)
        
        # Icon
        icon_label = tk.Label(
            title_left,
            text="🌐",
            font=("Segoe UI", 20),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Title and description
        title_text_frame = tk.Frame(title_left, bg=COLORS['CARD'])
        title_text_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        title_label = tk.Label(
            title_text_frame,
            text="Suivi Global Tickets",
            font=UIConfig.FONT_HEADER,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(
            title_text_frame,
            text="Agrégation automatique des suivis de communes",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        subtitle_label.pack(anchor=tk.W)
        
        # Action buttons on the right
        title_right = tk.Frame(title_content, bg=COLORS['CARD'])
        title_right.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Help button
        help_btn = ttk.Button(
            title_right,
            text="❓ Aide",
            command=self._show_help,
            style='Compact.TButton'
        )
        help_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Reset button
        reset_btn = ttk.Button(
            title_right,
            text="🔄 Réinitialiser",
            command=self._reset_module,
            style='CompactWarning.TButton'
        )
        reset_btn.pack(side=tk.RIGHT, padx=(5, 0))

    def _create_content_area(self):
        """Create the main content area."""
        # Content container
        content_container = tk.Frame(self.parent, bg=COLORS['BG'])
        content_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Configure columns
        content_container.grid_columnconfigure(0, weight=1, minsize=350)  # Left column
        content_container.grid_columnconfigure(1, weight=1, minsize=350)  # Right column
        
        # Left column - Folder selection and scanning
        left_column = tk.Frame(content_container, bg=COLORS['BG'])
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # Right column - Progress and generation
        right_column = tk.Frame(content_container, bg=COLORS['BG'])
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        # Create sections
        self._create_folder_selection_section(left_column)
        self._create_progress_section(right_column)
        self._create_generation_section(right_column)

    def _setup_module_shortcuts(self):
        """Set up keyboard shortcuts specific to this module."""
        self.keyboard_manager.set_callback("Control-s", self._scan_shortcut)
        self.keyboard_manager.set_callback("Control-g", self._generate_shortcut)
        self.keyboard_manager.set_callback("F5", self._refresh_shortcut)

    def _show_help(self):
        """Show help information for this module."""
        help_text = """
Suivi Global Tickets - Aide

Ce module permet d'agréger automatiquement les informations des fichiers de suivi de communes individuelles dans un fichier Excel global centralisé.

Fonctionnalités principales:
• Scan automatique du dossier Teams
• Extraction des données de la page 3 des fichiers de suivi
• Mise à jour du fichier Excel global "Suivis Global Tickets CMS Adr_PA"
• Préservation des données existantes lors des mises à jour

Instructions simplifiées:
1. Cliquez sur "Scanner et traiter les dossiers" pour analyser automatiquement
2. Vérifiez le résumé des données trouvées
3. Cliquez sur "Mettre à jour Excel Global" pour créer/mettre à jour le fichier

Le fichier global:
• Nom fixe: "Suivis Global Tickets CMS Adr_PA.xlsx"
• Page 1 (Suivi Tickets): Données de la page 3 des fichiers individuels
• Page 2 (Traitement CMS Adr): Données de la page 1 des fichiers individuels
• Page 3 (Traitement PA): Données de la page 2 des fichiers individuels
• Structure identique aux fichiers sources individuels
• Mise à jour intelligente (ajoute nouvelles communes, met à jour existantes)

Raccourcis clavier:
• Ctrl+S : Scanner et traiter
• Ctrl+G : Mettre à jour le fichier Excel
• F5 : Actualiser
        """
        messagebox.showinfo("Aide - Suivi Global Tickets", help_text)

    def _reset_module(self):
        """Reset the module to initial state."""
        try:
            self.commune_folders.clear()
            self.processed_data.clear()
            self.existing_communes.clear()
            self.new_communes.clear()
            self.updated_communes.clear()

            if self.summary_text:
                self.summary_text.delete(1.0, tk.END)

            if self.status_label:
                self.status_label.config(text="Prêt à scanner les dossiers")

            if self.progress_var:
                self.progress_var.set(0)

            if self.generate_button:
                self.generate_button.config(state=tk.DISABLED)

            if self.scan_button:
                self.scan_button.config(state=tk.NORMAL)

            # Clear statistics display
            if hasattr(self, 'stats_label'):
                self.stats_label.config(text="")

            self.logger.info("Module reset successfully")
            
        except Exception as e:
            self.logger.error(f"Error resetting module: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la réinitialisation:\n{e}")

    def _create_folder_selection_section(self, parent: tk.Widget):
        """Create the simplified folder scanning section."""
        # Scanning card
        scan_card = create_card_frame(parent)
        scan_card.pack(fill=tk.X, pady=(0, 10))

        # Section header
        header_frame = create_section_header(scan_card, "🔍", "Scan automatique")
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Content frame
        content_frame = tk.Frame(scan_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Description
        desc_label = tk.Label(
            content_frame,
            text="Scanne automatiquement le dossier Teams pour trouver et traiter les fichiers de suivi des communes.",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD'],
            wraplength=400,
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W, pady=(0, 15))

        # Single scan button
        self.scan_button = tk.Button(
            content_frame,
            text="🔍 Scanner et traiter les dossiers",
            command=self._scan_and_process_folders,
            bg=COLORS['SUCCESS'],
            fg='white',
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=25,
            pady=10
        )
        self.scan_button.pack(anchor=tk.W)

    def _create_progress_section(self, parent: tk.Widget):
        """Create the progress monitoring section."""
        # Progress card
        progress_card = create_card_frame(parent)
        progress_card.pack(fill=tk.X, pady=(0, 10))

        # Section header
        header_frame = create_section_header(progress_card, "📊", "Progression")
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Content frame
        content_frame = tk.Frame(progress_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            content_frame,
            variable=self.progress_var,
            maximum=100,
            length=300,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # Status label
        self.status_label = tk.Label(
            content_frame,
            text="Prêt à scanner les dossiers",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        self.status_label.pack(anchor=tk.W)

    def _create_generation_section(self, parent: tk.Widget):
        """Create the Excel generation section."""
        # Generation card
        generation_card = create_card_frame(parent)
        generation_card.pack(fill=tk.BOTH, expand=True)

        # Section header with statistics
        self.generation_header_frame = tk.Frame(generation_card, bg=COLORS['CARD'])
        self.generation_header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Main header
        header_icon = tk.Label(
            self.generation_header_frame,
            text="📈",
            font=("Segoe UI", 12),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        header_icon.pack(side=tk.LEFT, padx=(0, 8))

        self.generation_header_label = tk.Label(
            self.generation_header_frame,
            text="Génération Excel",
            font=UIConfig.FONT_HEADER,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        self.generation_header_label.pack(side=tk.LEFT)

        # Statistics label (will be updated after scan)
        self.stats_label = tk.Label(
            self.generation_header_frame,
            text="",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        self.stats_label.pack(side=tk.RIGHT)

        # Content frame
        content_frame = tk.Frame(generation_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Summary text area
        summary_label = tk.Label(
            content_frame,
            text="Résumé des données:",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        summary_label.pack(anchor=tk.W, pady=(0, 5))

        # Text widget with scrollbar
        text_frame = tk.Frame(content_frame, bg=COLORS['CARD'])
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        text_scrollbar = tk.Scrollbar(text_frame)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.summary_text = tk.Text(
            text_frame,
            yscrollcommand=text_scrollbar.set,
            font=UIConfig.FONT_SMALL,
            bg=COLORS['WHITE'],
            fg=COLORS['TEXT_SECONDARY'],
            height=8,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scrollbar.config(command=self.summary_text.yview)

        # Generation button
        self.generate_button = tk.Button(
            content_frame,
            text="📊 Mettre à jour Excel Global",
            command=self._update_global_excel,
            bg=COLORS['PRIMARY'],
            fg='white',
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=25,
            pady=10,
            state=tk.DISABLED
        )
        self.generate_button.pack(pady=(10, 0))

    def _load_existing_communes(self):
        """Load existing commune data from the global Excel file for comparison."""
        try:
            global_file_path = os.path.join(self.teams_folder_path, self.global_excel_filename)

            if not os.path.exists(global_file_path):
                self.logger.info("No existing global file found - all communes will be new")
                return {}

            pd = get_pandas()
            existing_df = pd.read_excel(
                global_file_path,
                sheet_name='Suivi Tickets',
                dtype={'Code INSEE': str, 'Insee': str},
                date_format=None  # CRITICAL: Prevent automatic date parsing
            )

            existing_communes = {}
            if not existing_df.empty and 'Nom Commune' in existing_df.columns:
                for _, row in existing_df.iterrows():
                    commune_name = str(row.get('Nom Commune', '')).strip()
                    insee_code = str(row.get('Code INSEE', '')).strip()
                    if commune_name:
                        # Use commune name as key, store additional info
                        existing_communes[commune_name] = {
                            'insee': insee_code,
                            'data': row.to_dict()
                        }

            self.logger.info(f"Loaded {len(existing_communes)} existing communes for comparison")
            return existing_communes

        except Exception as e:
            self.logger.error(f"Error loading existing communes: {e}")
            return {}

    def _analyze_commune_changes(self, processed_data):
        """Analyze which communes are new vs existing."""
        self.new_communes.clear()
        self.updated_communes.clear()

        for commune_data in processed_data:
            commune_name = commune_data['nom_commune']

            if commune_name in self.existing_communes:
                # Existing commune - will be updated
                self.updated_communes.append(commune_data)
            else:
                # New commune
                self.new_communes.append(commune_data)

        self.logger.info(f"Analysis: {len(self.new_communes)} new communes, {len(self.updated_communes)} communes to update")

    def _scan_and_process_folders(self):
        """Automatically scan Teams folder and process commune folders in one step."""
        try:
            self.status_label.config(text="Chargement des données existantes...")
            self.progress_var.set(10)
            self.scan_button.config(state=tk.DISABLED)

            def scan_and_process():
                # Step 1: Load existing commune data for comparison
                self.existing_communes = self._load_existing_communes()

                # Step 2: Auto-scan Teams folder
                from config.constants import TeamsConfig
                base_path = TeamsConfig.get_teams_base_path()

                if not os.path.exists(base_path):
                    raise Exception(f"Le dossier Teams n'a pas été trouvé: {base_path}")

                # Find commune folders
                found_folders = []
                for item in os.listdir(base_path):
                    item_path = os.path.join(base_path, item)
                    if os.path.isdir(item_path) and '_' in item:
                        # Check if folder contains Excel files
                        excel_files = [f for f in os.listdir(item_path) if f.endswith(('.xlsx', '.xls'))]
                        if excel_files:
                            found_folders.append(item_path)

                if not found_folders:
                    raise Exception("Aucun dossier de commune trouvé dans le répertoire Teams.")

                self.commune_folders = found_folders

                # Step 3: Process the folders
                processed_data = self._process_commune_folders()

                # Step 4: Analyze changes (new vs existing)
                self._analyze_commune_changes(processed_data)

                return processed_data

            def on_success(result):
                self.processed_data = result
                self._update_summary_display()
                self.generate_button.config(state=tk.NORMAL)
                self.scan_button.config(state=tk.NORMAL)

                # Enhanced status message with new/updated counts
                new_count = len(self.new_communes)
                updated_count = len(self.updated_communes)
                status_msg = f"Scan terminé - {new_count} nouvelles communes, {updated_count} à mettre à jour"
                self.status_label.config(text=status_msg)
                self.progress_var.set(100)

            def on_error(error):
                self.logger.error(f"Error in scan and process: {error}")
                self.status_label.config(text="Erreur lors du scan")
                self.progress_var.set(0)
                self.scan_button.config(state=tk.NORMAL)
                messagebox.showerror("Erreur", f"Erreur lors du scan:\n{error}")

            # Process asynchronously
            run_async_task(scan_and_process, on_success, on_error, "Scan and process")

        except Exception as e:
            self.logger.error(f"Error initiating scan and process: {e}")
            self.scan_button.config(state=tk.NORMAL)
            messagebox.showerror("Erreur", f"Erreur lors du lancement du scan:\n{e}")

    def _process_commune_folders(self) -> List[Dict[str, Any]]:
        """Process commune folders and extract data from page 3 of suivi files."""
        processed_data = []
        total_folders = len(self.commune_folders)

        for i, folder_path in enumerate(self.commune_folders):
            try:
                # Update progress with current commune being processed
                progress = 20 + (i / total_folders) * 60  # Reserve 20% for initial scan, 60% for processing
                self.progress_var.set(progress)

                # Update status with current commune
                folder_name = os.path.basename(folder_path)
                self.status_label.config(text=f"Traitement: {folder_name} ({i+1}/{total_folders})")
                self.parent.update()

                # Find Excel files in the folder
                excel_files = []
                for file in os.listdir(folder_path):
                    if file.endswith(('.xlsx', '.xls')) and 'suivi' in file.lower():
                        excel_files.append(os.path.join(folder_path, file))

                if not excel_files:
                    self.logger.warning(f"No suivi Excel files found in {folder_path}")
                    continue

                # Process the most recent suivi file
                latest_file = max(excel_files, key=os.path.getmtime)
                commune_data = self._extract_commune_data(latest_file, folder_path)

                if commune_data:
                    processed_data.append(commune_data)
                    self.logger.info(f"Processed commune: {commune_data.get('nom_commune', 'Unknown')}")

            except Exception as e:
                self.logger.error(f"Error processing folder {folder_path}: {e}")
                continue

        # Update status for analysis phase
        self.status_label.config(text="Analyse des nouvelles communes...")
        self.progress_var.set(85)
        self.parent.update()

        return processed_data

    def _extract_commune_data(self, excel_file_path: str, folder_path: str) -> Optional[Dict[str, Any]]:
        """Extract data from page 3 of a commune suivi file."""
        try:
            pd = get_pandas()

            # Read page 3 of the Excel file with INSEE as string to preserve leading zeros
            # Also prevent automatic date parsing to avoid date format issues
            df = pd.read_excel(
                excel_file_path,
                sheet_name=2,
                dtype={'Insee': str, 'insee': str, 'Code INSEE': str},
                date_format=None  # CRITICAL: Prevent automatic date parsing
            )  # Page 3 (0-indexed)

            if df.empty:
                self.logger.warning(f"Page 3 is empty in {excel_file_path}")
                return None

            # Extract commune information from the first row or filename
            folder_name = os.path.basename(folder_path)
            parts = folder_name.split('_')

            commune_data = {
                'nom_commune': parts[0] if len(parts) > 0 else 'Unknown',
                'id_tache': parts[1] if len(parts) > 1 else 'Unknown',
                'insee_code': self._extract_insee_from_data(df) or 'Unknown',
                'file_path': excel_file_path,
                'folder_path': folder_path,
                'last_modified': os.path.getmtime(excel_file_path),
                'data_summary': self._summarize_page3_data(df)
            }

            return commune_data

        except Exception as e:
            self.logger.error(f"Error extracting data from {excel_file_path}: {e}")
            return None

    def _extract_insee_from_data(self, df) -> Optional[str]:
        """Extract INSEE code from the dataframe."""
        try:
            # Look for INSEE code in common column names
            insee_columns = ['insee', 'code_insee', 'INSEE', 'Code INSEE', 'Insee']
            for col in insee_columns:
                if col in df.columns and not df[col].empty:
                    insee_value = df[col].iloc[0]
                    if insee_value is not None:
                        # Format INSEE code to preserve leading zeros (5 digits)
                        insee_str = str(insee_value).strip()
                        if insee_str.isdigit() and len(insee_str) <= 5:
                            return insee_str.zfill(5)  # Pad with leading zeros to 5 digits
                        return insee_str
            return None
        except:
            return None

    def _summarize_page3_data(self, df) -> Dict[str, Any]:
        """Summarize the data from page 3."""
        try:
            return {
                'total_rows': len(df),
                'columns': list(df.columns),
                'non_empty_rows': len(df.dropna(how='all'))
            }
        except:
            return {'total_rows': 0, 'columns': [], 'non_empty_rows': 0}

    def _update_summary_display(self):
        """Update the summary text display with detailed analysis of new vs existing communes."""
        try:
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)

            if not self.processed_data:
                self.summary_text.insert(tk.END, "Aucune donnée trouvée.")
                self.summary_text.config(state=tk.DISABLED)
                return

            new_count = len(self.new_communes)
            updated_count = len(self.updated_communes)
            total_count = len(self.processed_data)

            summary_lines = [
                f"=== ANALYSE DU SCAN ===",
                f"Total communes trouvées: {total_count}",
                f"🆕 Nouvelles communes: {new_count}",
                f"🔄 Communes à mettre à jour: {updated_count}",
                f""
            ]

            # Show new communes first
            if self.new_communes:
                summary_lines.extend([
                    f"=== 🆕 NOUVELLES COMMUNES ({new_count}) ===",
                ])
                for i, data in enumerate(self.new_communes, 1):
                    summary_lines.extend([
                        f"{i}. 🆕 {data['nom_commune']} (ID: {data['id_tache']})",
                        f"   INSEE: {data['insee_code']}",
                        f"   Lignes de données: {data['data_summary']['total_rows']}",
                        f"   Fichier: {os.path.basename(data['file_path'])}",
                        f""
                    ])

            # Show communes to update
            if self.updated_communes:
                summary_lines.extend([
                    f"=== 🔄 COMMUNES À METTRE À JOUR ({updated_count}) ===",
                ])
                for i, data in enumerate(self.updated_communes, 1):
                    summary_lines.extend([
                        f"{i}. 🔄 {data['nom_commune']} (ID: {data['id_tache']})",
                        f"   INSEE: {data['insee_code']}",
                        f"   Lignes de données: {data['data_summary']['total_rows']}",
                        f"   Fichier: {os.path.basename(data['file_path'])}",
                        f""
                    ])

            # Check if global file exists
            global_file_path = os.path.join(self.teams_folder_path, self.global_excel_filename)
            file_exists = os.path.exists(global_file_path)
            action = "mis à jour" if file_exists else "créé"

            summary_lines.extend([
                f"=== 📊 MISE À JOUR EXCEL ===",
                f"Le fichier Excel global sera {action}:",
                f"• Nom: {self.global_excel_filename}",
                f"• Page 1 (Suivi Tickets): Données de la page 3 des fichiers sources",
                f"• Page 2 (Traitement CMS Adr): Données de la page 1 des fichiers sources",
                f"• Page 3 (Traitement PA): Données de la page 2 des fichiers sources",
                f"• Nouvelles communes: {new_count} ajoutées sur toutes les pages",
                f"• Communes existantes: {updated_count} mises à jour sur toutes les pages",
                f"",
                f"📁 Emplacement: {self.teams_folder_path}",
                f"📋 Statut: {'Fichier existant - données préservées et mises à jour' if file_exists else 'Nouveau fichier - toutes les communes seront ajoutées'}"
            ])

            self.summary_text.insert(tk.END, "\n".join(summary_lines))
            self.summary_text.config(state=tk.DISABLED)

            # Update statistics in header
            if hasattr(self, 'stats_label'):
                stats_text = f"🆕 {new_count} nouvelles | 🔄 {updated_count} à mettre à jour"
                self.stats_label.config(text=stats_text)

        except Exception as e:
            self.logger.error(f"Error updating summary display: {e}")

    def _update_global_excel(self):
        """Update or create the global Excel file."""
        if not self.processed_data:
            messagebox.showwarning("Aucune donnée", "Aucune donnée à traiter. Veuillez d'abord scanner des dossiers.")
            return

        try:
            self.status_label.config(text="Mise à jour du fichier Excel...")
            self.progress_var.set(0)

            def update_process():
                return self._create_or_update_global_excel_file()

            def on_success(result):
                file_path, is_new = result
                action = "créé" if is_new else "mis à jour"
                self.status_label.config(text=f"Fichier Excel {action} avec succès")
                self.progress_var.set(100)

                # Prepare detailed success message
                new_count = len(self.new_communes)
                updated_count = len(self.updated_communes)
                total_count = len(self.processed_data)

                if is_new:
                    details = f"Nouveau fichier créé avec {total_count} communes."
                else:
                    details = f"Fichier mis à jour:\n• {new_count} nouvelles communes ajoutées\n• {updated_count} communes existantes mises à jour\n• Total: {total_count} communes traitées"

                # Show enhanced success message with statistics
                response = messagebox.askyesno(
                    "Mise à jour terminée",
                    f"Le fichier Excel global a été {action} avec succès!\n\n"
                    f"📊 Statistiques:\n{details}\n\n"
                    f"📁 Fichier: {os.path.basename(file_path)}\n"
                    f"📍 Emplacement: {os.path.dirname(file_path)}\n\n"
                    f"Voulez-vous ouvrir le fichier?"
                )

                if response:
                    self._open_generated_file(file_path)

            def on_error(error):
                self.logger.error(f"Error updating Excel: {error}")
                self.status_label.config(text="Erreur lors de la mise à jour")
                self.progress_var.set(0)
                messagebox.showerror("Erreur", f"Erreur lors de la mise à jour:\n{error}")

            # Update asynchronously
            run_async_task(update_process, on_success, on_error, "Excel update")

        except Exception as e:
            self.logger.error(f"Error initiating Excel update: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du lancement de la mise à jour:\n{e}")

    def _create_or_update_global_excel_file(self) -> tuple:
        """Create or update the global Excel file with aggregated data."""
        try:
            pd = get_pandas()

            # Ensure output directory exists
            os.makedirs(self.teams_folder_path, exist_ok=True)

            # Define the fixed filename
            file_path = os.path.join(self.teams_folder_path, self.global_excel_filename)

            # Check if file exists
            is_new_file = not os.path.exists(file_path)

            if is_new_file:
                # Create new file
                self.logger.info("Creating new global Excel file")
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    # Page 1: Aggregated data from page 3 of all communes (Suivi Tickets)
                    self._create_page1_aggregated_data(writer, None)

                    # Page 2: Aggregated data from page 1 of all communes (CM Adresse)
                    self._create_page2_cm_adresse_data(writer, None)

                    # Page 3: Aggregated data from page 2 of all communes (Plan Adressage)
                    self._create_page3_plan_adressage_data(writer, None)
            else:
                # Update existing file
                self.logger.info("Updating existing global Excel file")
                # Read existing data from all sheets
                existing_page1_df = None
                existing_page2_df = None
                existing_page3_df = None

                try:
                    existing_page1_df = pd.read_excel(
                        file_path,
                        sheet_name='Suivi Tickets',
                        dtype={'Code INSEE': str, 'Insee': str},
                        date_format=None  # CRITICAL: Prevent automatic date parsing
                    )
                    # Immediately apply date formatting to existing data
                    if existing_page1_df is not None and not existing_page1_df.empty:
                        existing_page1_df = self._format_date_columns(existing_page1_df)
                        self.logger.info("Applied date formatting to existing Suivi Tickets data")
                except:
                    pass
                try:
                    existing_page2_df = pd.read_excel(
                        file_path,
                        sheet_name='Traitement CMS Adr',
                        dtype={'Code INSEE': str, 'Insee': str},
                        date_format=None  # CRITICAL: Prevent automatic date parsing
                    )
                    # Immediately apply date formatting to existing data
                    if existing_page2_df is not None and not existing_page2_df.empty:
                        existing_page2_df = self._format_date_columns(existing_page2_df)
                        self.logger.info("Applied date formatting to existing Traitement CMS Adr data")
                except:
                    pass
                try:
                    existing_page3_df = pd.read_excel(
                        file_path,
                        sheet_name='Traitement PA',
                        dtype={'Code INSEE': str, 'Insee': str},
                        date_format=None  # CRITICAL: Prevent automatic date parsing
                    )
                    # Immediately apply date formatting to existing data
                    if existing_page3_df is not None and not existing_page3_df.empty:
                        existing_page3_df = self._format_date_columns(existing_page3_df)
                        self.logger.info("Applied date formatting to existing Traitement PA data")
                except:
                    pass

                # Update with new data
                with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                    self._create_page1_aggregated_data(writer, existing_page1_df)
                    self._create_page2_cm_adresse_data(writer, existing_page2_df)
                    self._create_page3_plan_adressage_data(writer, existing_page3_df)

            # Apply formatting similar to individual suivi files
            self._format_global_excel(file_path)

            self.logger.info(f"Global Excel file {'created' if is_new_file else 'updated'}: {file_path}")
            return file_path, is_new_file

        except Exception as e:
            self.logger.error(f"Error creating/updating global Excel file: {e}")
            raise

    def _format_date_columns(self, df):
        """Format date columns to display only date without time, excluding duration columns."""
        try:
            pd = get_pandas()

            # List of column names that should be treated as dates (expanded list)
            date_column_keywords = ['date', 'livraison', 'affectation', 'dépose', 'traitement']

            # List of column names that should NOT be treated as dates (duration/time columns)
            duration_column_keywords = ['durée', 'duration', 'temps', 'time', 'traitement optimum', 'finale', 'motif']

            for column in df.columns:
                column_lower = str(column).lower()

                # First check if it's a duration column - if so, skip it
                is_duration = any(keyword in column_lower for keyword in duration_column_keywords)
                if is_duration:
                    continue

                # Then check if it's a date column
                is_date = any(keyword in column_lower for keyword in date_column_keywords)
                if is_date:
                    # Convert datetime objects to date-only strings
                    for idx in df.index:
                        value = df.at[idx, column]
                        if pd.notna(value) and value != '':
                            try:
                                # Use the comprehensive date normalization method
                                normalized_date = self._normalize_date_value(value, column)
                                if normalized_date and normalized_date != str(value):
                                    df.at[idx, column] = normalized_date
                            except Exception as e:
                                self.logger.warning(f"Could not format date in column {column}: {e}")
                                continue

            return df

        except Exception as e:
            self.logger.error(f"Error formatting date columns: {e}")
            return df

    def _create_page1_aggregated_data(self, writer, existing_df=None):
        """Create page 1 with aggregated data from all communes, maintaining exact structure from source files."""
        try:
            pd = get_pandas()

            # Get the structure from the first commune's page 3 to maintain consistency
            template_structure = None
            if self.processed_data:
                try:
                    first_commune = self.processed_data[0]
                    template_df = pd.read_excel(first_commune['file_path'], sheet_name=2)
                    if not template_df.empty:
                        template_structure = list(template_df.columns)
                except Exception as e:
                    self.logger.warning(f"Could not get template structure: {e}")

            # Prepare aggregated data maintaining original structure
            aggregated_rows = []

            for commune_data in self.processed_data:
                try:
                    # Read the original page 3 data with INSEE as string to preserve leading zeros
                    # Also ensure date columns are read as strings to prevent automatic date conversion
                    original_df = pd.read_excel(
                        commune_data['file_path'],
                        sheet_name=2,
                        dtype={'Insee': str, 'insee': str, 'Code INSEE': str},
                        date_format=None  # Prevent automatic date parsing
                    )

                    if not original_df.empty:
                        # Format date columns to remove time component
                        original_df = self._format_date_columns(original_df)

                        # Take all rows from page 3 and add commune identification
                        for _, row in original_df.iterrows():
                            row_data = row.to_dict()

                            # Ensure we have commune identification columns at the beginning
                            # Handle different column name variations
                            if 'Nom Commune' not in row_data and 'Nom de commune' not in row_data:
                                row_data['Nom Commune'] = commune_data['nom_commune']
                            if 'Code INSEE' not in row_data:
                                row_data['Code INSEE'] = commune_data['insee_code']

                            # Normalize column names for consistency
                            if 'Nom de commune' in row_data and 'Nom Commune' not in row_data:
                                row_data['Nom Commune'] = row_data.pop('Nom de commune')

                            aggregated_rows.append(row_data)

                except Exception as e:
                    self.logger.error(f"Error processing commune {commune_data['nom_commune']}: {e}")
                    continue

            # Create DataFrame
            if aggregated_rows:
                new_df = pd.DataFrame(aggregated_rows)

                # If we have existing data, merge it
                if existing_df is not None and not existing_df.empty:
                    # Format date columns in existing data
                    existing_df = self._format_date_columns(existing_df)

                    # Remove existing entries for communes that are being updated
                    commune_names = [data['nom_commune'] for data in self.processed_data]
                    if 'Nom Commune' in existing_df.columns:
                        existing_df = existing_df[~existing_df['Nom Commune'].isin(commune_names)]

                    # Combine existing and new data
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True, sort=False)
                else:
                    combined_df = new_df

                # Ensure Nom Commune and Code INSEE are first columns
                columns = list(combined_df.columns)
                priority_columns = ['Nom Commune', 'Code INSEE']
                other_columns = [col for col in columns if col not in priority_columns]

                # Reorder columns
                final_columns = []
                for col in priority_columns:
                    if col in columns:
                        final_columns.append(col)
                final_columns.extend(other_columns)

                combined_df = combined_df.reindex(columns=final_columns)

                # Final validation and formatting of date columns before writing
                combined_df = self._validate_and_format_dates_before_writing(combined_df, 'Suivi Tickets')

                # Write to Excel with the correct sheet name
                combined_df.to_excel(writer, sheet_name='Suivi Tickets', index=False)

                # Format INSEE columns as text to preserve leading zeros
                self._format_insee_columns_as_text(writer, 'Suivi Tickets', combined_df)
            else:
                # Create empty DataFrame with basic headers
                empty_df = pd.DataFrame(columns=['Nom Commune', 'Code INSEE'])
                empty_df.to_excel(writer, sheet_name='Suivi Tickets', index=False)

        except Exception as e:
            self.logger.error(f"Error creating page 1 data: {e}")
            raise

    def _create_page2_cm_adresse_data(self, writer, existing_df=None):
        """Create page 2 with aggregated CM Adresse data from page 1 of all commune files."""
        try:
            pd = get_pandas()

            # Prepare aggregated data from CM Adresse sheets (page 1)
            aggregated_rows = []

            for commune_data in self.processed_data:
                try:
                    # Read the CM Adresse sheet (page 1) data with INSEE as string to preserve leading zeros
                    # Also ensure date columns are read as strings to prevent automatic date conversion
                    cm_df = pd.read_excel(
                        commune_data['file_path'],
                        sheet_name=0,
                        dtype={'Insee': str, 'insee': str, 'Code INSEE': str},
                        date_format=None  # Prevent automatic date parsing
                    )  # First sheet

                    # Debug: Log the actual columns found
                    self.logger.info(f"CM Adresse columns in {commune_data['nom_commune']}: {list(cm_df.columns)}")

                    if not cm_df.empty:
                        # Format date columns to remove time component
                        cm_df = self._format_date_columns(cm_df)

                        # Extract specific columns for Page 2 based on actual CM Adresse structure
                        for _, row in cm_df.iterrows():
                            row_data = {}

                            # Map columns based on actual CM Adresse structure from excel_generator.py
                            columns = cm_df.columns.tolist()
                            self.logger.debug(f"CM Adresse columns found: {columns}")

                            # Extract required columns based on actual structure
                            # From excel_generator.py: CM Adresse columns are:
                            # A: Nom commune, B: Insee, C: ID Tache, D: Voie demandé, E: Motif Voie,
                            # F: CODE RIVOLI, G: GPS (X,Y), H: Centre/Zone, I: Status PC, J: Descriptif Commentaire,
                            # K: Collaborateur, L: Date affectation, M: Date traitement, N: Date livraison, O: Durée, P: STATUT Ticket

                            row_data['Nom commune'] = self._get_column_value(row, columns, ['Nom commune'], commune_data['nom_commune'])
                            row_data['Insee'] = self._get_column_value(row, columns, ['Insee'], commune_data['insee_code'])
                            row_data['ID Tache'] = self._get_column_value(row, columns, ['ID Tache'], '')
                            row_data['Collaborateur'] = self._get_column_value(row, columns, ['Collaborateur'], '')
                            row_data['Date affectation'] = self._get_column_value(row, columns, ['Date affectation'], '')
                            row_data['Date traitement'] = self._get_column_value(row, columns, ['Date traitement'], '')
                            row_data['Date livraison'] = self._get_column_value(row, columns, ['Date livraison'], '')
                            row_data['STATUT Ticket'] = self._get_column_value(row, columns, ['STATUT Ticket'], '')

                            aggregated_rows.append(row_data)

                except Exception as e:
                    self.logger.error(f"Error processing CM Adresse data for commune {commune_data['nom_commune']}: {e}")
                    continue

            # Create DataFrame
            if aggregated_rows:
                new_df = pd.DataFrame(aggregated_rows)

                # Apply comprehensive date formatting to the new DataFrame
                new_df = self._format_date_columns(new_df)
                self.logger.info(f"Applied date formatting to new CM Adresse data: {len(new_df)} rows")

                # If we have existing data, merge it
                if existing_df is not None and not existing_df.empty:
                    # Format date columns in existing data
                    existing_df = self._format_date_columns(existing_df)

                    # Remove existing entries for communes that are being updated
                    commune_names = [data['nom_commune'] for data in self.processed_data]
                    if 'Nom commune' in existing_df.columns:
                        existing_df = existing_df[~existing_df['Nom commune'].isin(commune_names)]

                    # Combine existing and new data
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True, sort=False)
                else:
                    combined_df = new_df

                # Ensure proper column order
                column_order = ['Nom commune', 'Insee', 'ID Tache', 'Collaborateur',
                               'Date affectation', 'Date traitement', 'Date livraison', 'STATUT Ticket']

                # Reorder columns, keeping any additional columns at the end
                existing_columns = list(combined_df.columns)
                final_columns = []
                for col in column_order:
                    if col in existing_columns:
                        final_columns.append(col)
                        existing_columns.remove(col)
                final_columns.extend(existing_columns)  # Add any remaining columns

                combined_df = combined_df.reindex(columns=final_columns)

                # Apply aggressive date formatting specifically for problematic columns
                combined_df = self._force_date_formatting_for_pages_2_3(combined_df, 'Traitement CMS Adr')

                # Final validation and formatting of date columns before writing
                combined_df = self._validate_and_format_dates_before_writing(combined_df, 'Traitement CMS Adr')

                # Write to Excel with the correct sheet name
                combined_df.to_excel(writer, sheet_name='Traitement CMS Adr', index=False)

                # Format INSEE columns as text to preserve leading zeros
                self._format_insee_columns_as_text(writer, 'Traitement CMS Adr', combined_df)
            else:
                # Create empty DataFrame with headers
                empty_df = pd.DataFrame(columns=['Nom commune', 'Insee', 'ID Tache', 'Collaborateur',
                                               'Date affectation', 'Date traitement', 'Date livraison', 'STATUT Ticket'])
                empty_df.to_excel(writer, sheet_name='Traitement CMS Adr', index=False)

        except Exception as e:
            self.logger.error(f"Error creating page 2 CM Adresse data: {e}")
            raise

    def _create_page3_plan_adressage_data(self, writer, existing_df=None):
        """Create page 3 with aggregated Plan Adressage data from page 2 of all commune files."""
        try:
            pd = get_pandas()

            # Prepare aggregated data from Plan Adressage sheets (page 2)
            aggregated_rows = []

            for commune_data in self.processed_data:
                try:
                    # Read the Plan Adressage sheet (page 2) data with INSEE as string to preserve leading zeros
                    # Also ensure date columns are read as strings to prevent automatic date conversion
                    plan_df = pd.read_excel(
                        commune_data['file_path'],
                        sheet_name=1,
                        dtype={'Insee': str, 'insee': str, 'Code INSEE': str},
                        date_format=None  # Prevent automatic date parsing
                    )  # Second sheet

                    # Debug: Log the actual columns found
                    self.logger.info(f"Plan Adressage columns in {commune_data['nom_commune']}: {list(plan_df.columns)}")

                    if not plan_df.empty:
                        # Format date columns to remove time component (but preserve duration columns)
                        plan_df = self._format_date_columns(plan_df)

                        # Extract specific columns for Page 3
                        for _, row in plan_df.iterrows():
                            row_data = {}

                            # Map columns based on expected structure
                            columns = plan_df.columns.tolist()

                            # Extract required columns based on actual Plan Adressage structure
                            # From excel_generator.py: Plan Adressage has commune info added at beginning:
                            # A: Nom commune, B: Insee, then original columns including:
                            # C: Num Dossier Site, D: Num Voie Site, E: Comp Voie Site, F: Libelle Voie Site,
                            # G: Batiment IMB, H: Motif, I: Même Adresse, J: Numero Voie BAN, K: Repondant Voie BAN,
                            # L: Libelle Voie BAN, M: Collaborateur, N: Date traitement, O: Durée, P: Traitement Optimum

                            row_data['Nom commune'] = self._get_column_value(row, columns, ['Nom commune'], commune_data['nom_commune'])
                            row_data['Insee'] = self._get_column_value(row, columns, ['Insee'], commune_data['insee_code'])
                            row_data['Num Dossier Site'] = self._get_column_value(row, columns, ['Num Dossier Site'], '')
                            row_data['Motif'] = self._get_column_value(row, columns, ['Motif'], '')
                            row_data['Adresse BAN'] = self._get_column_value(row, columns, ['Adresse BAN'], '')
                            row_data['Collaborateur'] = self._get_column_value(row, columns, ['Collaborateur'], '')
                            row_data['Date traitement'] = self._get_column_value(row, columns, ['Date traitement'], '')
                            row_data['Durée'] = self._get_column_value(row, columns, ['Durée'], '')

                            aggregated_rows.append(row_data)

                except Exception as e:
                    self.logger.error(f"Error processing Plan Adressage data for commune {commune_data['nom_commune']}: {e}")
                    continue

            # Create DataFrame
            if aggregated_rows:
                new_df = pd.DataFrame(aggregated_rows)

                # Apply comprehensive date formatting to the new DataFrame
                new_df = self._format_date_columns(new_df)
                self.logger.info(f"Applied date formatting to new Plan Adressage data: {len(new_df)} rows")

                # If we have existing data, merge it
                if existing_df is not None and not existing_df.empty:
                    # Format date columns in existing data
                    existing_df = self._format_date_columns(existing_df)

                    # Remove existing entries for communes that are being updated
                    commune_names = [data['nom_commune'] for data in self.processed_data]
                    if 'Nom commune' in existing_df.columns:
                        existing_df = existing_df[~existing_df['Nom commune'].isin(commune_names)]

                    # Combine existing and new data
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True, sort=False)
                else:
                    combined_df = new_df

                # Remove obsolete columns if they exist
                obsolete_columns = ['Batiment IMB', 'Traitement Optimum']
                for col in obsolete_columns:
                    if col in combined_df.columns:
                        combined_df = combined_df.drop(columns=[col])
                        self.logger.info(f"Removed obsolete column: {col}")

                # Ensure proper column order for Plan Adressage page
                column_order = ['Nom commune', 'Insee', 'Num Dossier Site', 'Motif', 'Adresse BAN',
                               'Collaborateur', 'Date traitement', 'Durée']

                # Reorder columns, keeping any additional columns at the end
                existing_columns = list(combined_df.columns)
                final_columns = []
                for col in column_order:
                    if col in existing_columns:
                        final_columns.append(col)
                        existing_columns.remove(col)
                final_columns.extend(existing_columns)  # Add any remaining columns

                combined_df = combined_df.reindex(columns=final_columns)

                # Apply aggressive date formatting specifically for problematic columns
                combined_df = self._force_date_formatting_for_pages_2_3(combined_df, 'Traitement PA')

                # Final validation and formatting of date columns before writing
                combined_df = self._validate_and_format_dates_before_writing(combined_df, 'Traitement PA')

                # Write to Excel with the correct sheet name
                combined_df.to_excel(writer, sheet_name='Traitement PA', index=False)

                # Format INSEE columns as text to preserve leading zeros
                self._format_insee_columns_as_text(writer, 'Traitement PA', combined_df)
            else:
                # Create empty DataFrame with headers for Plan Adressage
                empty_df = pd.DataFrame(columns=['Nom commune', 'Insee', 'Num Dossier Site', 'Motif', 'Adresse BAN',
                                               'Collaborateur', 'Date traitement', 'Durée'])
                empty_df.to_excel(writer, sheet_name='Traitement PA', index=False)

        except Exception as e:
            self.logger.error(f"Error creating page 3 Plan Adressage data: {e}")
            raise

    def _get_column_value(self, row, columns, possible_names, default_value=''):
        """Get value from row using possible column names, with fallback to default."""
        try:
            pd = get_pandas()
            for name in possible_names:
                if name in columns:
                    value = row[name]
                    # Return the value if it's not null/empty, otherwise continue to next possible name
                    if pd.notna(value) and str(value).strip() != '':
                        # Special handling for INSEE codes to preserve leading zeros
                        if any(insee_keyword in name.lower() for insee_keyword in ['insee', 'code insee']):
                            # Format INSEE code to preserve leading zeros (5 digits)
                            insee_str = str(value).strip()
                            if insee_str.isdigit() and len(insee_str) <= 5:
                                return insee_str.zfill(5)  # Pad with leading zeros to 5 digits
                            return insee_str

                        # Special handling for date columns to ensure consistent dd/mm/yyyy format
                        elif any(date_keyword in name.lower() for date_keyword in ['date', 'livraison', 'affectation', 'dépose', 'traitement']):
                            # Skip duration columns
                            if not any(duration_keyword in name.lower() for duration_keyword in ['durée', 'duration', 'temps', 'time', 'optimum']):
                                normalized_value = self._normalize_date_value(value, name)
                                # Debug logging for date conversion
                                if str(value) != normalized_value:
                                    self.logger.debug(f"Date conversion in column '{name}': '{value}' -> '{normalized_value}'")
                                return normalized_value

                        return value

            # If no valid value found in any of the possible columns, return default
            return default_value

        except Exception as e:
            self.logger.warning(f"Error getting column value for {possible_names}: {e}")
            return default_value

    def _format_insee_columns_as_text(self, writer, sheet_name: str, df):
        """Format INSEE columns as text to preserve leading zeros."""
        try:
            # Get the worksheet
            worksheet = writer.sheets[sheet_name]

            # Find INSEE columns
            insee_columns = []
            for i, col_name in enumerate(df.columns):
                if any(insee_keyword in col_name.lower() for insee_keyword in ['insee', 'code insee']):
                    insee_columns.append(i + 1)  # Excel columns are 1-indexed

            # Format INSEE columns as text
            for col_idx in insee_columns:
                col_letter = chr(64 + col_idx)  # Convert to Excel column letter (A, B, C, etc.)

                # Set the entire column format to text
                for row_idx in range(2, len(df) + 2):  # Start from row 2 (skip header)
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    if cell.value is not None:
                        # Ensure the value is formatted as text with leading zeros
                        insee_str = str(cell.value).strip()
                        if insee_str.isdigit() and len(insee_str) <= 5:
                            cell.value = insee_str.zfill(5)  # Pad with leading zeros
                        cell.number_format = '@'  # Text format

            self.logger.info(f"Formatted INSEE columns as text in sheet: {sheet_name}")

        except Exception as e:
            self.logger.warning(f"Error formatting INSEE columns in {sheet_name}: {e}")

    def _format_global_excel(self, file_path: str):
        """Apply formatting to the global Excel file similar to individual suivi files."""
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, PatternFill, Alignment

            # Load workbook
            wb = load_workbook(file_path)

            # Format all sheets with consistent styling
            sheets_to_format = ['Suivi Tickets', 'Traitement CMS Adr', 'Traitement PA']

            for sheet_name in sheets_to_format:
                if sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    self._format_sheet(ws, sheet_name)
                    # Apply additional date formatting to ensure consistency
                    self._apply_comprehensive_date_formatting(ws, sheet_name)

            # Save changes
            wb.save(file_path)
            self.logger.info(f"Global Excel file formatted successfully: {file_path}")

        except Exception as e:
            self.logger.error(f"Error formatting Excel file: {e}")
            # Don't raise - formatting is optional

    def _format_sheet(self, ws, sheet_name):
        """Apply consistent formatting to a worksheet."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment

            # Header formatting (blue background, centered, frozen)
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            header_font = Font(color='FFFFFF', bold=True)
            center_alignment = Alignment(horizontal='center', vertical='center')

            # Apply header formatting
            if ws.max_row > 0:
                for cell in ws[1]:
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = center_alignment

                # Freeze header row
                ws.freeze_panes = 'A2'

            # Center all content and apply date formatting
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        cell.alignment = center_alignment

                        # Apply date formatting to date columns
                        if cell.row > 1:  # Skip header row
                            column_letter = cell.column_letter
                            header_cell = ws[f'{column_letter}1']
                            header_value = str(header_cell.value).lower() if header_cell.value else ""

                            # First check if it's a duration column - if so, skip date formatting
                            duration_keywords = ['durée', 'duration', 'temps', 'time', 'traitement optimum', 'finale', 'motif']
                            is_duration = any(keyword in header_value for keyword in duration_keywords)

                            # Only apply date formatting if it's not a duration column
                            if not is_duration:
                                date_keywords = ['date', 'livraison', 'affectation', 'dépose', 'traitement']
                                is_date = any(keyword in header_value for keyword in date_keywords)
                                if is_date and cell.value is not None and str(cell.value).strip() != '':
                                    # Apply dd/mm/yyyy format
                                    cell.number_format = 'DD/MM/YYYY'

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            self.logger.info(f"Formatting applied to sheet: {sheet_name}")

        except Exception as e:
            self.logger.error(f"Error formatting sheet {sheet_name}: {e}")

    def _apply_comprehensive_date_formatting(self, ws, sheet_name):
        """Apply comprehensive date formatting to ensure all date columns use dd/mm/yyyy format."""
        try:
            # Define date column patterns for each sheet type
            date_patterns = {
                'Suivi Tickets': ['date', 'livraison', 'affectation', 'dépose'],
                'Traitement CMS Adr': ['date', 'livraison', 'affectation', 'traitement'],
                'Traitement PA': ['date', 'traitement']
            }

            # Get patterns for this sheet
            patterns = []
            for sheet_type, sheet_patterns in date_patterns.items():
                if sheet_type.lower() in sheet_name.lower():
                    patterns = sheet_patterns
                    break

            # If no specific patterns, use general date patterns
            if not patterns:
                patterns = ['date', 'livraison', 'affectation', 'dépose', 'traitement']

            # Duration columns to exclude
            duration_keywords = ['durée', 'duration', 'temps', 'time', 'traitement optimum', 'finale', 'motif']

            # Check each column
            for col in range(1, ws.max_column + 1):
                header_cell = ws.cell(row=1, column=col)
                if header_cell.value:
                    header_text = str(header_cell.value).lower()

                    # Skip duration columns
                    is_duration = any(keyword in header_text for keyword in duration_keywords)
                    if is_duration:
                        continue

                    # Check if it's a date column
                    is_date = any(pattern in header_text for pattern in patterns)
                    if is_date:
                        # Apply date formatting to all cells in this column
                        for row in range(2, ws.max_row + 1):
                            cell = ws.cell(row=row, column=col)
                            if cell.value is not None and str(cell.value).strip() != '':
                                cell.number_format = 'DD/MM/YYYY'

                        self.logger.debug(f"Applied date formatting to column {header_text} in {sheet_name}")

        except Exception as e:
            self.logger.warning(f"Error applying comprehensive date formatting to {sheet_name}: {e}")

    def _normalize_date_value(self, value, column_name=''):
        """Normalize a date value to dd/mm/yyyy format string with proper European date handling."""
        try:
            pd = get_pandas()

            if pd.isna(value) or value == '' or value is None:
                return ''

            # If it's already a string, handle carefully
            if isinstance(value, str):
                value_str = str(value).strip()

                # If empty after stripping, return empty
                if not value_str:
                    return ''

                # Check if it's already in dd/mm/yyyy format (European)
                if len(value_str) == 10 and value_str.count('/') == 2:
                    parts = value_str.split('/')
                    if len(parts) == 3 and all(part.isdigit() for part in parts):
                        day, month, year = parts
                        if len(day) == 2 and len(month) == 2 and len(year) == 4:
                            # Validate the date components
                            try:
                                day_int, month_int = int(day), int(month)
                                if 1 <= day_int <= 31 and 1 <= month_int <= 12:
                                    return value_str  # Already in correct dd/mm/yyyy format
                            except:
                                pass

                # Try multiple parsing strategies for European dates
                try:
                    # Strategy 1: Force European format (day first)
                    if '/' in value_str:
                        parts = value_str.split('/')
                        if len(parts) == 3:
                            # Assume dd/mm/yyyy or d/m/yyyy format
                            day, month, year = parts[0].zfill(2), parts[1].zfill(2), parts[2]
                            if len(year) == 2:
                                year = '20' + year if int(year) < 50 else '19' + year
                            elif len(year) == 4:
                                pass
                            else:
                                raise ValueError("Invalid year format")

                            # Validate and return
                            day_int, month_int, year_int = int(day), int(month), int(year)
                            if 1 <= day_int <= 31 and 1 <= month_int <= 12 and 1900 <= year_int <= 2100:
                                return f"{day}/{month}/{year}"

                    # Strategy 2: Use pandas with dayfirst=True
                    parsed_date = pd.to_datetime(value_str, dayfirst=True, format='mixed')
                    return parsed_date.strftime('%d/%m/%Y')

                except:
                    # Strategy 3: Try common European formats explicitly
                    european_formats = ['%d/%m/%Y', '%d/%m/%y', '%d-%m-%Y', '%d-%m-%y', '%d.%m.%Y', '%d.%m.%y']
                    for fmt in european_formats:
                        try:
                            from datetime import datetime
                            parsed_date = datetime.strptime(value_str, fmt)
                            return parsed_date.strftime('%d/%m/%Y')
                        except:
                            continue

                    # If all parsing fails, return original
                    self.logger.warning(f"Could not parse date string '{value_str}' in column '{column_name}'")
                    return value_str

            # If it's a datetime object
            elif hasattr(value, 'strftime'):
                return value.strftime('%d/%m/%Y')

            # For other types, try to convert to string and parse
            else:
                try:
                    # Convert to string and try parsing
                    str_value = str(value)
                    parsed_date = pd.to_datetime(str_value, dayfirst=True)
                    return parsed_date.strftime('%d/%m/%Y')
                except:
                    return str(value)  # Return as string if can't parse

        except Exception as e:
            self.logger.warning(f"Error normalizing date value '{value}' in column '{column_name}': {e}")
            return str(value) if value is not None else ''

    def _validate_and_format_dates_before_writing(self, df, sheet_name):
        """Final validation and formatting of date columns before writing to Excel."""
        try:
            pd = get_pandas()

            # Define date columns for each sheet type
            date_columns_map = {
                'Suivi Tickets': ['Date d\'affectation', 'Date Livraison', 'Date Dépose Ticket', 'Date traitement', 'Date livraison'],
                'Traitement CMS Adr': ['Date affectation', 'Date traitement', 'Date livraison'],
                'Traitement PA': ['Date traitement']
            }

            # Get relevant date columns for this sheet
            relevant_date_columns = []
            for sheet_type, columns in date_columns_map.items():
                if sheet_type.lower() in sheet_name.lower():
                    relevant_date_columns = columns
                    break

            # Also check for any column containing date keywords
            date_keywords = ['date', 'livraison', 'affectation', 'dépose', 'traitement']
            duration_keywords = ['durée', 'duration', 'temps', 'time', 'optimum', 'motif']

            for column in df.columns:
                column_lower = str(column).lower()

                # Skip duration columns
                is_duration = any(keyword in column_lower for keyword in duration_keywords)
                if is_duration:
                    continue

                # Check if it's a date column (either in the specific list or contains date keywords)
                is_date_column = (column in relevant_date_columns or
                                any(keyword in column_lower for keyword in date_keywords))

                if is_date_column:
                    self.logger.info(f"Validating and formatting date column '{column}' in {sheet_name}")

                    # Process each value in the date column
                    for idx in df.index:
                        value = df.at[idx, column]
                        if pd.notna(value) and str(value).strip() != '':
                            original_value = str(value)
                            normalized_value = self._normalize_date_value(value, column)

                            if normalized_value != original_value:
                                df.at[idx, column] = normalized_value
                                self.logger.debug(f"Final date formatting in {sheet_name}, column '{column}': '{original_value}' -> '{normalized_value}'")

            return df

        except Exception as e:
            self.logger.error(f"Error validating dates before writing {sheet_name}: {e}")
            return df

    def _force_date_formatting_for_pages_2_3(self, df, sheet_name):
        """Aggressively format date columns specifically for pages 2 and 3 to fix inversion issues."""
        try:
            pd = get_pandas()

            # Define the exact problematic columns for each sheet
            problematic_date_columns = {
                'Traitement CMS Adr': ['Date affectation', 'Date traitement', 'Date livraison'],
                'Traitement PA': ['Date traitement']
            }

            # Get the columns to fix for this sheet
            columns_to_fix = []
            for sheet_type, columns in problematic_date_columns.items():
                if sheet_type.lower() in sheet_name.lower():
                    columns_to_fix = columns
                    break

            if not columns_to_fix:
                return df

            self.logger.info(f"Applying aggressive date formatting to {sheet_name} for columns: {columns_to_fix}")

            # Process each problematic column
            for column in columns_to_fix:
                if column in df.columns:
                    self.logger.info(f"Processing column '{column}' in {sheet_name}")

                    for idx in df.index:
                        value = df.at[idx, column]
                        if pd.notna(value) and str(value).strip() != '':
                            original_value = str(value)

                            # Apply multiple strategies to ensure European format
                            normalized_value = self._aggressive_european_date_normalization(value, column)

                            if normalized_value != original_value:
                                df.at[idx, column] = normalized_value
                                self.logger.debug(f"Date formatting applied - Column '{column}': '{original_value}' -> '{normalized_value}'")
                            else:
                                self.logger.debug(f"No change needed for '{original_value}' in column '{column}'")

            return df

        except Exception as e:
            self.logger.error(f"Error in aggressive date formatting for {sheet_name}: {e}")
            return df

    def _aggressive_european_date_normalization(self, value, column_name):
        """Aggressively normalize dates to European format, handling common inversion cases."""
        try:
            pd = get_pandas()

            if pd.isna(value) or value == '' or value is None:
                return ''

            value_str = str(value).strip()
            if not value_str:
                return ''

            # CRITICAL FIX: Handle the specific inversion pattern we identified
            # Pattern: dd/mm/yyyy being read as mm/dd/yyyy
            if '/' in value_str and len(value_str) == 10:
                parts = value_str.split('/')
                if len(parts) == 3:
                    try:
                        part1, part2, year = parts[0], parts[1], parts[2]

                        # If we have a pattern like 06/01/2025 or 06/07/2025
                        # These are likely inverted from 01/06/2025 or 07/06/2025
                        if len(part1) == 2 and len(part2) == 2 and len(year) == 4:
                            day_candidate = int(part1)
                            month_candidate = int(part2)

                            # CONSERVATIVE APPROACH: Only fix when we're absolutely certain
                            # Only apply inversion fix if both parts are <= 12 (ambiguous case)
                            # AND we're in a date column that's known to have inversion issues
                            if (day_candidate <= 12 and month_candidate <= 12 and
                                any(keyword in column_name.lower() for keyword in ['date', 'traitement', 'livraison', 'affectation'])):

                                # Apply the inversion fix
                                corrected_date = f"{part2.zfill(2)}/{part1.zfill(2)}/{year}"
                                return corrected_date

                            # For all other cases, keep the original format
                            else:
                                return value_str

                    except (ValueError, IndexError):
                        pass

            # Handle datetime objects first
            if hasattr(value, 'strftime'):
                # For datetime objects, always format as European dd/mm/yyyy
                return value.strftime('%d/%m/%Y')

            # Handle string dates with aggressive European parsing
            if isinstance(value, str) or isinstance(value_str, str):
                # Remove any time components
                if ' ' in value_str:
                    value_str = value_str.split(' ')[0]

                # Handle different separators
                separators = ['/', '-', '.']
                for sep in separators:
                    if sep in value_str:
                        parts = value_str.split(sep)
                        if len(parts) == 3:
                            try:
                                # Parse as dd/mm/yyyy (European format)
                                day_str, month_str, year_str = parts[0], parts[1], parts[2]

                                # Handle 2-digit years
                                if len(year_str) == 2:
                                    year_int = int(year_str)
                                    year_str = '20' + year_str if year_int < 50 else '19' + year_str

                                # Ensure 2-digit day and month
                                day_str = day_str.zfill(2)
                                month_str = month_str.zfill(2)

                                # Validate ranges
                                day_int = int(day_str)
                                month_int = int(month_str)
                                year_int = int(year_str)

                                # Check if this could be a US format that needs conversion
                                if month_int > 12 and day_int <= 12:
                                    # Likely US format (mm/dd/yyyy) - swap day and month
                                    original_day_str, original_month_str = day_str, month_str
                                    day_str, month_str = month_str, day_str
                                    day_int, month_int = month_int, day_int
                                    print(f"🔄 DETECTED US FORMAT: '{value_str}' in column '{column_name}' - Converting {original_month_str}/{original_day_str} to {day_str}/{month_str} (European format)")
                                    self.logger.warning(f"🔄 DETECTED US FORMAT: '{value_str}' in column '{column_name}' - Converting {original_month_str}/{original_day_str} to {day_str}/{month_str} (European format)")

                                # Final validation
                                if 1 <= day_int <= 31 and 1 <= month_int <= 12 and 1900 <= year_int <= 2100:
                                    result = f"{day_str}/{month_str}/{year_str}"
                                    return result

                            except (ValueError, IndexError):
                                continue

                # If no separator found or parsing failed, try pandas with European preference
                try:
                    # Force European format parsing
                    parsed_date = pd.to_datetime(value_str, dayfirst=True, errors='coerce')
                    if pd.notna(parsed_date):
                        return parsed_date.strftime('%d/%m/%Y')
                except:
                    pass

                # Last resort: return original if it looks like a valid date
                if len(value_str) >= 8 and any(char.isdigit() for char in value_str):
                    self.logger.warning(f"Could not parse date '{value_str}' in column '{column_name}', keeping original")
                    return value_str

            return str(value)

        except Exception as e:
            self.logger.error(f"Error in aggressive date normalization for '{value}' in column '{column_name}': {e}")
            return str(value) if value is not None else ''



    def _open_generated_file(self, file_path: str):
        """Open the generated Excel file."""
        try:
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', file_path])
            else:  # Linux
                subprocess.call(['xdg-open', file_path])

        except Exception as e:
            self.logger.error(f"Error opening file: {e}")
            messagebox.showerror("Erreur", f"Impossible d'ouvrir le fichier:\n{e}")

    def _restore_session(self):
        """Restore previous session data."""
        try:
            if not self.session_manager:
                return

            session_data = self.session_manager.load_session()

            # Restore folder list
            folders = session_data.get('commune_folders', [])
            for folder in folders:
                if os.path.exists(folder) and folder not in self.commune_folders:
                    self.commune_folders.append(folder)
                    self.folder_listbox.insert(tk.END, os.path.basename(folder))

            self.logger.info("Session restored successfully")

        except Exception as e:
            self.logger.error(f"Error restoring session: {e}")

    # Keyboard shortcut methods
    def _scan_shortcut(self):
        """Keyboard shortcut for scanning folders."""
        if self.scan_button['state'] == tk.NORMAL:
            self._scan_and_process_folders()

    def _generate_shortcut(self):
        """Keyboard shortcut for generating Excel."""
        if self.generate_button['state'] == tk.NORMAL:
            self._update_global_excel()

    def _refresh_shortcut(self):
        """Keyboard shortcut for refreshing."""
        self._reset_module()
