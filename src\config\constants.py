"""
Configuration constants for the Suivi Generator application.
Centralized configuration management for colors, validation lists, and UI settings.
"""

# Color scheme for the application - Inspired by Sofrecom design language
COLORS = {
    'PRIMARY': "#0066CC",       # Bleu Sofrecom principal
    'PRIMARY_LIGHT': "#3385D6", # Bleu clair
    'PRIMARY_DARK': "#004499",  # Bleu foncé
    'SECONDARY': "#FF6600",     # Orange Sofrecom (accent)
    'SECONDARY_LIGHT': "#FF8533", # Orange clair
    'SUCCESS': "#28A745",       # Vert moderne
    'WARNING': "#FFC107",       # Jaune d'avertissement
    'DANGER': "#DC3545",        # Rouge d'erreur
    'INFO': "#495057",          # Gris informatif plus foncé
    'TEXT_SECONDARY': "#6C757D", # Texte secondaire
    'TEXT_MUTED': "#ADB5BD",    # Texte atténué
    'LIGHT': "#F8F9FA",         # Gris très clair
    'WHITE': "#FFFFFF",         # Blanc pur
    'BORDER': "#E9ECEF",        # Bordure subtile
    'SHADOW': "#00000015",      # Ombre légère
    'BG': "#FFFFFF",           # Arrière-plan principal blanc
    'CARD': "#FFFFFF",         # Arrière-plan des cartes
    'CARD_HOVER': "#F8F9FA",   # Arrière-plan des cartes au hover
    'ACCENT': "#E3F2FD",       # Bleu très clair pour les accents
    'GRADIENT_START': "#0066CC", # Début du gradient
    'GRADIENT_END': "#004499"   # Fin du gradient
}

# Validation lists for dropdown menus
VALIDATION_LISTS = {
    "Domaine": ["Orange", "RIP"],
    "Type de Commune": ["Classique", "Fusionné"],
    "Type de base": ["Mono-Base", "Multi-Base"],
    "Motif Voie": ["Création Voie", "Modification Voie", "Rien à faire"],
    "STATUT Ticket": ["Traité", "En Cours", "En Attente", "Rejeté"],
    "Etat": ["Traité", "En Cours", "En Attente", "Rejeté"],
    "Depose Ticket UPR": ["Non Créé", "Créé"],
    "PC Status": ["PC trouvé sur la voie", "PC fictif ajouté"],
    "XY Status": ["RAS", "MàJ XY effectué dans Oras", "Modification libélle 42C"],
    "Collaborateur": [
        "BACHOUEL Iheb",
        "BEN ALI Mariem",
        "ELJ Wissem",
        "OUESLATI Mohamed Amine",
        "ZAOUGA Wissem"
    ]
}

# UI Configuration
class UIConfig:
    """UI configuration constants"""
    
    # Window settings
    WINDOW_TITLE = "Générateur Suivi CM Adresse/ Plan Adressage"
    WINDOW_MIN_SIZE = "680x530"
    WINDOW_DEFAULT_SIZE = "700x550"
    
    # Typography - Sofrecom inspired, professional and readable
    FONT_TITLE = ("Segoe UI", 11, "bold")
    FONT_SUBTITLE = ("Segoe UI", 9)
    FONT_BUTTON = ("Segoe UI", 9, "bold")
    FONT_HEADER = ("Segoe UI", 14, "bold")
    FONT_SUBHEADER = ("Segoe UI", 10)
    FONT_SMALL = ("Segoe UI", 8)
    FONT_LARGE = ("Segoe UI", 16, "bold")
    FONT_CARD_TITLE = ("Segoe UI", 12, "bold")
    FONT_CARD_SUBTITLE = ("Segoe UI", 9)
    FONT_CARD_DESCRIPTION = ("Segoe UI", 8)

# File processing configuration
class FileConfig:
    """File processing configuration"""
    
    # QGis columns to import
    QGIS_COLUMNS = "A,B,C,D,G,J,O,P,Q,R"
    QGIS_COLUMNS_WITH_U = "A,B,C,D,G,J,O,P,Q,R,U"
    
    # Supported file types
    EXCEL_FILETYPES = [("Fichiers Excel", "*.xlsx *.xls")]
    
    # Column mappings for Plan Adressage
    PLAN_ADRESSAGE_COLUMNS = [
        'Num Dossier Site',      # Colonne A du fichier source
        'Num Voie Site',         # Colonne B du fichier source
        'Comp Voie Site',        # Colonne C du fichier source
        'Libelle Voie Site',     # Colonne D du fichier source
        'Batiment IMB',          # Colonne G du fichier source
        'Motif',                 # Colonne J du fichier source
        'Même Adresse',          # Colonne O du fichier source
        'Numero Voie BAN',       # Colonne P du fichier source
        'Repondant Voie BAN',    # Colonne Q du fichier source
        'Libelle Voie BAN'       # Colonne R du fichier source
    ]
    
    PLAN_ADRESSAGE_COLUMNS_WITH_U = PLAN_ADRESSAGE_COLUMNS + ['Adresse BAN']  # Colonne U

# Application metadata
class AppInfo:
    """Application information"""

    VERSION = "2.1"
    AUTHOR = "Equipe BLI"
    COPYRIGHT = "© 2025 Sofrecom Tunisie"
    DESCRIPTION = "SofreTrack Pro - Solutions de traitement et génération de données"
    FULL_DESCRIPTION = "SofreTrack Pro :Une Initiative pour automatiser la génération des suivis pour l'activité CMS Adresse & Plan Adressage"

# Logging configuration
class LoggingConfig:
    """Logging configuration constants"""

    DEFAULT_LEVEL = "INFO"
    FILE_LOGGING = True
    LOG_FORMAT_CONSOLE = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FORMAT_FILE = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    BACKUP_COUNT = 5



# Teams Channel configuration
class TeamsConfig:
    """Microsoft Teams channel configuration"""

    @staticmethod
    def get_teams_base_path():
        """
        Get the Teams base path dynamically based on current user.

        Returns:
            str: Teams base path for current user
        """
        import os
        import getpass

        # Get current username
        username = getpass.getuser()

        # Construct Teams path for current user
        teams_path = rf"C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements"

        return teams_path

    @staticmethod
    def get_global_teams_path():
        """
        Get the Teams global path for Suivi Global module.

        Returns:
            str: Teams global path for current user
        """
        import os
        import getpass

        # Get current username
        username = getpass.getuser()

        # Construct Teams global path for current user
        global_path = rf"C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage"

        return global_path

    # Legacy property for backward compatibility
    @property
    def TEAMS_BASE_PATH(self):
        return self.get_teams_base_path()

    # Folder naming pattern: NomCommune_IdTache
    FOLDER_NAME_PATTERN = "{nom_commune}_{id_tache}"

    # Enable automatic Teams saving (can be disabled for testing)
    ENABLE_TEAMS_SAVING = True

    # Fallback to file dialog if Teams path is not accessible
    FALLBACK_TO_DIALOG = True
