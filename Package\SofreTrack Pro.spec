# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['..\\src\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('../Icone_App.png', '.'), ('../Icone_App_Sharp.ico', '.'), ('../logo_Sofrecom.png', '.')],
    hiddenimports=['pandas', 'openpyxl', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'tkinter', 'getpass', 'subprocess'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SofreTrack Pro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['..\\Icone_App_Sharp.ico'],
)
