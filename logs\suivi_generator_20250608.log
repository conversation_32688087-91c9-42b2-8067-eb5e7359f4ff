2025-06-08 00:04:39 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 00:04:39 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 00:04:39 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 00:04:39 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 00:04:39 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 00:04:39 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 00:04:39 - main - INFO - setup_application:45 - ============================================================
2025-06-08 00:04:39 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 00:04:39 - main - INFO - setup_application:47 - Version: 2.0.3
2025-06-08 00:04:39 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-08 00:04:39 - main - INFO - setup_application:49 - ============================================================
2025-06-08 00:04:39 - main - INFO - main:99 - Creating application...
2025-06-08 00:04:40 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 00:04:40 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 00:04:41 - ui.navigation - INFO - register_module:189 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 00:04:41 - ui.navigation - INFO - register_module:189 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 00:04:41 - ui.navigation - INFO - register_module:189 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 00:04:41 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:04:41 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 00:04:42 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:04:42 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 00:04:42 - main - INFO - main:102 - Application created successfully
2025-06-08 00:04:42 - main - INFO - main:103 - Starting main loop...
2025-06-08 00:04:42 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 00:04:42 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 00:04:57 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:04:57 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:04:57 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:04:57 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:04:57 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:04:57 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:04:57 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:04:57 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:04:57 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:04:57 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:04:59 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:04:59 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:05:05 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: settings
2025-06-08 00:05:05 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: settings
2025-06-08 00:05:05 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:05:05 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:05:56 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: settings
2025-06-08 00:05:56 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: settings
2025-06-08 00:05:59 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:05:59 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:06:12 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:06:12 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:06:12 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:06:12 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:06:12 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:06:12 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:06:13 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:06:13 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:06:13 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:06:13 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 00:06:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:06:16 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 00:06:16 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 00:06:22 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:06:22 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:06:24 - ui.home_screen - INFO - _open_team_stats:351 - User clicked Team Statistics button
2025-06-08 00:06:24 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: team_stats
2025-06-08 00:06:24 - ui.navigation - INFO - _load_module:268 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 00:06:24 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:06:24 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 00:06:24 - ui.navigation - INFO - _load_module:293 - Module team_stats created and loaded successfully
2025-06-08 00:06:24 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: team_stats
2025-06-08 00:06:25 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:06:25 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 00:06:26 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 00:07:16 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:07:16 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:07:17 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:07:17 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:07:17 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:07:17 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:07:17 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:07:17 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:07:17 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:07:17 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:07:17 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:07:17 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:07:18 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:07:18 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:07:21 - ui.home_screen - INFO - _open_suivi_generator:321 - User clicked Suivi Generator button
2025-06-08 00:07:21 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_generator
2025-06-08 00:07:21 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 00:07:21 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 00:07:21 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 00:07:21 - ui.navigation - INFO - _load_module:293 - Module suivi_generator created and loaded successfully
2025-06-08 00:07:21 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_generator
2025-06-08 00:07:21 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:07:21 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 00:07:21 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 00:07:38 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-08 00:07:38 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-08 00:07:38 - ui.components.generation - INFO - reset:208 - Generation section reset
2025-06-08 00:07:38 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-08 00:07:38 - ui.modules.suivi_generator_module - INFO - _reset_module:518 - Module reset successfully
2025-06-08 00:08:19 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-08 00:08:19 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-08 00:08:19 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-08 00:08:19 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-08 00:08:19 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-08 00:08:19 - ui.modules.suivi_generator_module - INFO - on_success:322 - MOAI file processed successfully
2025-06-08 00:08:22 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-08 00:08:22 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-08 00:08:22 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-08 00:08:22 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-08 00:08:22 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-08 00:08:22 - ui.modules.suivi_generator_module - INFO - on_success:344 - QGis file processed successfully
2025-06-08 00:08:26 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING
2025-06-08 00:08:26 - utils.file_utils - INFO - create_teams_folder:333 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING
2025-06-08 00:08:26 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING
2025-06-08 00:08:26 - utils.file_utils - INFO - get_teams_file_path:359 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING\Suivi_SELIGNE_TESTING_79312.xlsx
2025-06-08 00:08:28 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING\Suivi_SELIGNE_TESTING_79312.xlsx
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: TESTING-CM Adresse
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: TESTING-CM Adresse
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['O'] in sheet: TESTING-Plan Adressage
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: TESTING-Plan Adressage
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: TESTING-Informations Commune
2025-06-08 00:08:28 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: TESTING-Informations Commune
2025-06-08 00:08:28 - core.excel_generator - INFO - _add_data_validations:373 - Data validations added to CM Adresse sheet
2025-06-08 00:08:28 - core.excel_generator - INFO - _create_validation_sheet:399 - Validation sheet created
2025-06-08 00:08:28 - core.excel_generator - INFO - _add_duration_formula:451 - Duration formulas added
2025-06-08 00:08:28 - core.excel_generator - INFO - _add_commune_validations:490 - Commune validations added
2025-06-08 00:08:28 - core.excel_generator - INFO - _add_plan_adressage_validations:519 - Plan Adressage validations added
2025-06-08 00:08:28 - core.excel_generator - INFO - generate_excel_file:83 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_TESTING\Suivi_SELIGNE_TESTING_79312.xlsx
2025-06-08 00:09:40 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-08 00:09:40 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-08 00:09:40 - ui.components.generation - INFO - reset:208 - Generation section reset
2025-06-08 00:09:40 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-08 00:09:40 - ui.modules.suivi_generator_module - INFO - _reset_module:518 - Module reset successfully
2025-06-08 00:09:41 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:09:42 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:09:43 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:09:43 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:09:43 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:09:43 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:09:43 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:09:43 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:09:43 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:09:43 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:09:43 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:09:43 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 00:09:44 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 7 communes to update
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 00:11:26 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 00:11:27 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:11:28 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:11:28 - ui.home_screen - INFO - _open_suivi_generator:321 - User clicked Suivi Generator button
2025-06-08 00:11:28 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_generator
2025-06-08 00:11:28 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 00:11:28 - ui.modules.suivi_generator_module - INFO - cleanup:602 - Module cleanup completed
2025-06-08 00:11:28 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 00:11:28 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 00:11:28 - ui.navigation - INFO - _load_module:293 - Module suivi_generator created and loaded successfully
2025-06-08 00:11:29 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_generator
2025-06-08 00:11:29 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:11:29 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 00:11:29 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 00:11:33 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-08 00:11:33 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 07393, Commune: TUNIS
2025-06-08 00:11:33 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx
2025-06-08 00:11:33 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-08 00:11:33 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-08 00:11:33 - ui.modules.suivi_generator_module - INFO - on_success:322 - MOAI file processed successfully
2025-06-08 00:11:35 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-08 00:11:35 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-08 00:11:35 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-08 00:11:35 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-08 00:11:35 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-08 00:11:35 - ui.modules.suivi_generator_module - INFO - on_success:344 - QGis file processed successfully
2025-06-08 00:11:40 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing
2025-06-08 00:11:40 - utils.file_utils - INFO - create_teams_folder:333 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing
2025-06-08 00:11:40 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing
2025-06-08 00:11:40 - utils.file_utils - INFO - get_teams_file_path:359 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing\Suivi_TUNIS_testing_07393.xlsx
2025-06-08 00:11:41 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing\Suivi_TUNIS_testing_07393.xlsx
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: testing-CM Adresse
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testing-CM Adresse
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['O'] in sheet: testing-Plan Adressage
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testing-Plan Adressage
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: testing-Informations Commune
2025-06-08 00:11:41 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testing-Informations Commune
2025-06-08 00:11:41 - core.excel_generator - INFO - _add_data_validations:373 - Data validations added to CM Adresse sheet
2025-06-08 00:11:41 - core.excel_generator - INFO - _create_validation_sheet:399 - Validation sheet created
2025-06-08 00:11:41 - core.excel_generator - INFO - _add_duration_formula:451 - Duration formulas added
2025-06-08 00:11:41 - core.excel_generator - INFO - _add_commune_validations:490 - Commune validations added
2025-06-08 00:11:41 - core.excel_generator - INFO - _add_plan_adressage_validations:519 - Plan Adressage validations added
2025-06-08 00:11:41 - core.excel_generator - INFO - generate_excel_file:83 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testing\Suivi_TUNIS_testing_07393.xlsx
2025-06-08 00:11:43 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:11:43 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:11:44 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:11:44 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:11:44 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:11:44 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:11:44 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:11:44 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:11:44 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:11:44 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:11:44 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:11:44 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: TUNIS
2025-06-08 00:11:45 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 1 new communes, 6 communes to update
2025-06-08 00:11:50 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:11:50 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:11:59 - ui.home_screen - INFO - _open_team_stats:351 - User clicked Team Statistics button
2025-06-08 00:11:59 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: team_stats
2025-06-08 00:11:59 - ui.navigation - INFO - _load_module:268 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 00:11:59 - ui.modules.team_stats_module - INFO - cleanup:1567 - Team Stats module cleaned up
2025-06-08 00:11:59 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:11:59 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 00:11:59 - ui.navigation - INFO - _load_module:293 - Module team_stats created and loaded successfully
2025-06-08 00:11:59 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: team_stats
2025-06-08 00:11:59 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:11:59 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 00:12:00 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 00:12:06 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:12:06 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:14:29 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: settings
2025-06-08 00:14:30 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: settings
2025-06-08 00:20:55 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:20:55 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:20:56 - main - INFO - main:107 - Application closed normally
2025-06-08 00:24:46 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 00:24:46 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 00:24:46 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 00:24:46 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 00:24:46 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 00:24:46 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 00:24:46 - main - INFO - setup_application:45 - ============================================================
2025-06-08 00:24:46 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 00:24:46 - main - INFO - setup_application:47 - Version: 2.0.3
2025-06-08 00:24:46 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-08 00:24:46 - main - INFO - setup_application:49 - ============================================================
2025-06-08 00:24:46 - main - INFO - main:99 - Creating application...
2025-06-08 00:24:47 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 00:24:47 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 00:24:48 - ui.navigation - INFO - register_module:189 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 00:24:48 - ui.navigation - INFO - register_module:189 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 00:24:48 - ui.navigation - INFO - register_module:189 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 00:24:48 - ui.navigation - INFO - register_module:189 - Registered module: Canal Teams (teams_access)
2025-06-08 00:24:48 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:24:48 - ui.main_window - INFO - _set_window_icon:173 - Window icon set successfully
2025-06-08 00:24:49 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:24:49 - ui.main_window - INFO - _setup_navigation:134 - Navigation system initialized
2025-06-08 00:24:49 - main - INFO - main:102 - Application created successfully
2025-06-08 00:24:49 - main - INFO - main:103 - Starting main loop...
2025-06-08 00:24:49 - ui.main_window - INFO - run:189 - Starting application main loop
2025-06-08 00:24:49 - ui.main_window - INFO - _post_init:151 - Main window initialization complete
2025-06-08 00:24:50 - ui.home_screen - INFO - _open_teams_access:366 - User clicked Teams Access button
2025-06-08 00:24:50 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: teams_access
2025-06-08 00:24:50 - ui.navigation - INFO - _load_module:268 - Loading module: teams_access (Canal Teams)
2025-06-08 00:24:51 - ui.modules.teams_access_module - INFO - _create_module_ui:54 - Teams Access module UI created successfully
2025-06-08 00:24:51 - ui.navigation - INFO - _load_module:293 - Module teams_access created and loaded successfully
2025-06-08 00:24:51 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: teams_access
2025-06-08 00:25:00 - ui.modules.teams_access_module - ERROR - _open_teams_folder:254 - Error opening Teams folder: Command '['explorer', 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage']' returned non-zero exit status 1.
2025-06-08 00:25:08 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:25:08 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:25:09 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: about
2025-06-08 00:25:09 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: about
2025-06-08 00:25:10 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:25:10 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:25:39 - ui.home_screen - INFO - _open_teams_access:366 - User clicked Teams Access button
2025-06-08 00:25:39 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: teams_access
2025-06-08 00:25:39 - ui.navigation - INFO - _load_module:268 - Loading module: teams_access (Canal Teams)
2025-06-08 00:25:39 - ui.modules.teams_access_module - INFO - _create_module_ui:54 - Teams Access module UI created successfully
2025-06-08 00:25:39 - ui.navigation - INFO - _load_module:293 - Module teams_access created and loaded successfully
2025-06-08 00:25:39 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: teams_access
2025-06-08 00:25:47 - ui.modules.teams_access_module - ERROR - _open_teams_folder:254 - Error opening Teams folder: Command '['explorer', 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage']' returned non-zero exit status 1.
2025-06-08 00:25:54 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:25:54 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:26:06 - ui.home_screen - INFO - _open_suivi_generator:321 - User clicked Suivi Generator button
2025-06-08 00:26:06 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_generator
2025-06-08 00:26:06 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 00:26:06 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 00:26:06 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 00:26:06 - ui.navigation - INFO - _load_module:293 - Module suivi_generator created and loaded successfully
2025-06-08 00:26:07 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:26:07 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-08 00:26:07 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-08 00:26:07 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 00:26:07 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 00:26:07 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_generator
2025-06-08 00:26:13 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx
2025-06-08 00:26:14 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-08 00:26:14 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 07393, Commune: TUNIS
2025-06-08 00:26:14 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-08 00:26:14 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-08 00:26:14 - ui.modules.suivi_generator_module - INFO - on_success:322 - MOAI file processed successfully
2025-06-08 00:26:15 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-08 00:26:15 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-08 00:26:15 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-08 00:26:15 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-08 00:26:15 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-08 00:26:15 - ui.modules.suivi_generator_module - INFO - on_success:344 - QGis file processed successfully
2025-06-08 00:26:18 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin
2025-06-08 00:26:18 - utils.file_utils - INFO - create_teams_folder:333 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin
2025-06-08 00:26:18 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin
2025-06-08 00:26:18 - utils.file_utils - INFO - get_teams_file_path:359 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin\Suivi_TUNIS_testin_07393.xlsx
2025-06-08 00:26:20 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin\Suivi_TUNIS_testin_07393.xlsx
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: testin-CM Adresse
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testin-CM Adresse
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['O'] in sheet: testin-Plan Adressage
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testin-Plan Adressage
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_date_formatting:276 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: testin-Informations Commune
2025-06-08 00:26:20 - core.excel_generator - INFO - _apply_sheet_styling:240 - Styling applied to sheet: testin-Informations Commune
2025-06-08 00:26:20 - core.excel_generator - INFO - _add_data_validations:373 - Data validations added to CM Adresse sheet
2025-06-08 00:26:20 - core.excel_generator - INFO - _create_validation_sheet:399 - Validation sheet created
2025-06-08 00:26:20 - core.excel_generator - INFO - _add_duration_formula:451 - Duration formulas added
2025-06-08 00:26:20 - core.excel_generator - INFO - _add_commune_validations:490 - Commune validations added
2025-06-08 00:26:20 - core.excel_generator - INFO - _add_plan_adressage_validations:519 - Plan Adressage validations added
2025-06-08 00:26:20 - core.excel_generator - INFO - generate_excel_file:83 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_testin\Suivi_TUNIS_testin_07393.xlsx
2025-06-08 00:26:26 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:26:27 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:26:29 - ui.home_screen - INFO - _open_team_stats:351 - User clicked Team Statistics button
2025-06-08 00:26:30 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: team_stats
2025-06-08 00:26:30 - ui.navigation - INFO - _load_module:268 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 00:26:30 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:26:30 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 00:26:30 - ui.navigation - INFO - _load_module:293 - Module team_stats created and loaded successfully
2025-06-08 00:26:30 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: team_stats
2025-06-08 00:26:30 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:26:30 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 00:26:31 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 00:26:41 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 00:26:43 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:26:43 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:26:46 - ui.home_screen - INFO - _open_suivi_global:336 - User clicked Suivi Global Tickets button
2025-06-08 00:26:46 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: suivi_global
2025-06-08 00:26:46 - ui.navigation - INFO - _load_module:268 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 00:26:46 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:26:46 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 00:26:46 - ui.navigation - INFO - _load_module:293 - Module suivi_global created and loaded successfully
2025-06-08 00:26:46 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: suivi_global
2025-06-08 00:26:46 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:26:46 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 00:26:46 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 00:26:47 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 00:26:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: TUNIS
2025-06-08 00:26:48 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 1 new communes, 6 communes to update
2025-06-08 00:26:51 - ui.navigation - INFO - navigate_to:210 - Attempting to navigate to: home
2025-06-08 00:26:51 - ui.navigation - INFO - navigate_to:245 - Successfully navigated to: home
2025-06-08 00:29:49 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 00:29:49 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 00:29:49 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 00:29:49 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 00:29:49 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 00:29:49 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 00:29:49 - main - INFO - setup_application:45 - ============================================================
2025-06-08 00:29:49 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 00:29:49 - main - INFO - setup_application:47 - Version: 2.0.3
2025-06-08 00:29:49 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-08 00:29:49 - main - INFO - setup_application:49 - ============================================================
2025-06-08 00:29:49 - main - INFO - main:99 - Creating application...
2025-06-08 00:29:51 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 00:29:51 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 00:29:51 - ui.navigation - INFO - register_module:188 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 00:29:51 - ui.navigation - INFO - register_module:188 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 00:29:51 - ui.navigation - INFO - register_module:188 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 00:29:51 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 00:29:51 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 00:29:53 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 00:29:53 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 00:29:53 - main - INFO - main:102 - Application created successfully
2025-06-08 00:29:53 - main - INFO - main:103 - Starting main loop...
2025-06-08 00:29:53 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 00:29:53 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 00:30:18 - ui.home_screen - INFO - _open_teams_folder_directly:371 - User clicked Teams folder button - opening directly
2025-06-08 00:30:19 - ui.home_screen - ERROR - _open_teams_folder_directly:391 - Error opening Teams folder: Command '['explorer', 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage']' returned non-zero exit status 1.
2025-06-08 00:32:24 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: about
2025-06-08 00:32:24 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: about
2025-06-08 00:33:09 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 00:33:09 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 00:33:09 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 00:33:09 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 00:33:09 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 00:33:09 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 00:33:09 - main - INFO - setup_application:45 - ============================================================
2025-06-08 00:33:09 - root - ERROR - main:115 - Failed to start application: type object 'AppInfo' has no attribute 'DESCRIPTION'
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\main.py", line 93, in main
    logger = setup_application()
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\main.py", line 46, in setup_application
    logger.info(f"Starting {AppInfo.DESCRIPTION}")
                            ^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'AppInfo' has no attribute 'DESCRIPTION'. Did you mean: 'FULL_DESCRIPTION'?
2025-06-08 00:35:01 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 00:35:01 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 00:35:01 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 00:35:01 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 00:35:01 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 00:35:01 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 00:35:01 - main - INFO - setup_application:45 - ============================================================
2025-06-08 00:35:01 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 00:35:01 - main - INFO - setup_application:47 - Version: 2.1
2025-06-08 00:35:01 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-08 00:35:01 - main - INFO - setup_application:49 - ============================================================
2025-06-08 00:35:01 - main - INFO - main:99 - Creating application...
2025-06-08 00:35:02 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 00:35:02 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 00:35:02 - ui.navigation - INFO - register_module:188 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 00:35:02 - ui.navigation - INFO - register_module:188 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 00:35:02 - ui.navigation - INFO - register_module:188 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 00:35:02 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 00:35:02 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 00:35:04 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 00:35:04 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 00:35:04 - main - INFO - main:102 - Application created successfully
2025-06-08 00:35:04 - main - INFO - main:103 - Starting main loop...
2025-06-08 00:35:04 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 00:35:04 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 00:35:14 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: about
2025-06-08 00:35:14 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: about
2025-06-08 00:35:26 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 00:35:26 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 00:35:28 - ui.home_screen - INFO - _open_teams_folder_directly:371 - User clicked Teams folder button - opening directly
2025-06-08 00:35:29 - ui.home_screen - INFO - _open_teams_folder_directly:380 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 00:35:32 - ui.home_screen - INFO - _open_teams_folder_directly:371 - User clicked Teams folder button - opening directly
2025-06-08 00:35:34 - ui.home_screen - INFO - _open_teams_folder_directly:380 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 00:35:46 - ui.home_screen - INFO - _open_team_stats:351 - User clicked Team Statistics button
2025-06-08 00:35:46 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 00:35:46 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 00:35:46 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 00:35:46 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 00:35:46 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 00:35:46 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 00:35:46 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 00:35:46 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 00:35:48 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 00:35:52 - main - INFO - main:107 - Application closed normally
2025-06-08 01:37:34 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250608.log
2025-06-08 01:37:34 - root - INFO - setup_logging:79 - ==================================================
2025-06-08 01:37:34 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-08 01:37:34 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-08 01:37:34 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-08 01:37:34 - root - INFO - setup_logging:83 - ==================================================
2025-06-08 01:37:34 - main - INFO - setup_application:45 - ============================================================
2025-06-08 01:37:34 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-08 01:37:34 - main - INFO - setup_application:47 - Version: 2.1
2025-06-08 01:37:34 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-08 01:37:34 - main - INFO - setup_application:49 - ============================================================
2025-06-08 01:37:34 - main - INFO - main:99 - Creating application...
2025-06-08 01:37:35 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-08 01:37:35 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-08 01:37:36 - ui.navigation - INFO - register_module:188 - Registered module: Générateur Suivi (suivi_generator)
2025-06-08 01:37:36 - ui.navigation - INFO - register_module:188 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-08 01:37:36 - ui.navigation - INFO - register_module:188 - Registered module: Statistiques Équipe (team_stats)
2025-06-08 01:37:36 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:37:36 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-08 01:37:37 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:37:37 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-08 01:37:37 - main - INFO - main:102 - Application created successfully
2025-06-08 01:37:37 - main - INFO - main:103 - Starting main loop...
2025-06-08 01:37:37 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-08 01:37:37 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-08 01:38:38 - ui.home_screen - INFO - _open_suivi_generator:358 - User clicked Suivi Generator button
2025-06-08 01:38:38 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:38:38 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:38:38 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:38:38 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:38:38 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:38:38 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:38:38 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-08 01:38:38 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-08 01:38:38 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 01:38:38 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 01:38:38 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:38:41 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:38:42 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:39:33 - ui.home_screen - INFO - _open_suivi_generator:358 - User clicked Suivi Generator button
2025-06-08 01:39:33 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:39:33 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:39:33 - ui.modules.suivi_generator_module - INFO - cleanup:602 - Module cleanup completed
2025-06-08 01:39:33 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:39:33 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:39:33 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:39:33 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:39:33 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-08 01:39:33 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-08 01:39:33 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 01:39:33 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 01:39:33 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:39:37 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-08 01:39:37 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-08 01:39:37 - ui.components.generation - INFO - reset:208 - Generation section reset
2025-06-08 01:39:37 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-08 01:39:37 - ui.modules.suivi_generator_module - INFO - _reset_module:518 - Module reset successfully
2025-06-08 01:39:40 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:39:40 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:39:41 - ui.home_screen - INFO - _open_teams_folder_directly:408 - User clicked Teams folder button - opening directly
2025-06-08 01:39:43 - ui.home_screen - INFO - _open_teams_folder_directly:417 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-08 01:39:47 - ui.home_screen - INFO - _open_suivi_global:373 - User clicked Suivi Global Tickets button
2025-06-08 01:39:47 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_global
2025-06-08 01:39:47 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 01:39:47 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:39:47 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 01:39:47 - ui.navigation - INFO - _load_module:292 - Module suivi_global created and loaded successfully
2025-06-08 01:39:48 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_global
2025-06-08 01:39:48 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:39:48 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 01:39:48 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 01:39:50 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:39:50 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:39:51 - ui.home_screen - INFO - _open_team_stats:388 - User clicked Team Statistics button
2025-06-08 01:39:51 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 01:39:51 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 01:39:51 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:39:52 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 01:39:52 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 01:39:52 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 01:39:52 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:39:52 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 01:39:54 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 01:40:19 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:40:19 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:41:28 - ui.home_screen - INFO - _open_suivi_global:373 - User clicked Suivi Global Tickets button
2025-06-08 01:41:28 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_global
2025-06-08 01:41:28 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 01:41:28 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:41:28 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 01:41:28 - ui.navigation - INFO - _load_module:292 - Module suivi_global created and loaded successfully
2025-06-08 01:41:28 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_global
2025-06-08 01:41:28 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:41:28 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 01:41:28 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 01:41:30 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:30 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:31 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:32 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:33 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:34 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:34 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:35 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:36 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:37 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:38 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:39 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-08 01:41:40 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-08 01:41:46 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:41:47 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:41:49 - ui.home_screen - INFO - _open_suivi_generator:358 - User clicked Suivi Generator button
2025-06-08 01:41:49 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:41:49 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:41:49 - ui.modules.suivi_generator_module - INFO - cleanup:602 - Module cleanup completed
2025-06-08 01:41:49 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:41:49 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:41:49 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:41:49 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:41:49 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 01:41:49 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 01:41:49 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:41:52 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:41:52 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:41:54 - ui.home_screen - INFO - _open_team_stats:388 - User clicked Team Statistics button
2025-06-08 01:41:54 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 01:41:54 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 01:41:54 - ui.modules.team_stats_module - INFO - cleanup:1567 - Team Stats module cleaned up
2025-06-08 01:41:54 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:41:54 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 01:41:54 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 01:41:54 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:41:54 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 01:41:54 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 01:41:57 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:41:57 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:41:58 - ui.home_screen - INFO - _open_suivi_generator:358 - User clicked Suivi Generator button
2025-06-08 01:41:58 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:41:58 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:41:58 - ui.modules.suivi_generator_module - INFO - cleanup:602 - Module cleanup completed
2025-06-08 01:41:58 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:41:58 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:41:58 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:41:59 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:41:59 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 01:41:59 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 01:41:59 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:42:01 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:42:01 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:42:05 - ui.home_screen - INFO - _open_suivi_global:373 - User clicked Suivi Global Tickets button
2025-06-08 01:42:05 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_global
2025-06-08 01:42:05 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-08 01:42:05 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:42:05 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-08 01:42:05 - ui.navigation - INFO - _load_module:292 - Module suivi_global created and loaded successfully
2025-06-08 01:42:05 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_global
2025-06-08 01:42:05 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:42:05 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-08 01:42:05 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-08 01:42:09 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:42:09 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:42:17 - ui.home_screen - INFO - _open_team_stats:388 - User clicked Team Statistics button
2025-06-08 01:42:17 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 01:42:17 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 01:42:17 - ui.modules.team_stats_module - INFO - cleanup:1567 - Team Stats module cleaned up
2025-06-08 01:42:17 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:42:17 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 01:42:17 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 01:42:17 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 01:42:17 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:42:17 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 01:42:19 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 01:44:21 - ui.modules.team_stats_module - INFO - _reset_module:344 - Module reset successfully
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 01:44:23 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 01:44:33 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:44:33 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:44:37 - ui.home_screen - INFO - _open_team_stats:388 - User clicked Team Statistics button
2025-06-08 01:44:37 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: team_stats
2025-06-08 01:44:37 - ui.navigation - INFO - _load_module:267 - Loading module: team_stats (Statistiques Équipe)
2025-06-08 01:44:37 - ui.modules.team_stats_module - INFO - cleanup:1567 - Team Stats module cleaned up
2025-06-08 01:44:37 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-08 01:44:37 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-08 01:44:37 - ui.navigation - INFO - _load_module:292 - Module team_stats created and loaded successfully
2025-06-08 01:44:38 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:44:38 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-08 01:44:38 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: team_stats
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-08 01:44:39 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-08 01:44:42 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:44:42 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:44:43 - ui.home_screen - INFO - _open_suivi_generator:358 - User clicked Suivi Generator button
2025-06-08 01:44:43 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: suivi_generator
2025-06-08 01:44:43 - ui.navigation - INFO - _load_module:267 - Loading module: suivi_generator (Générateur Suivi)
2025-06-08 01:44:43 - ui.modules.suivi_generator_module - INFO - cleanup:602 - Module cleanup completed
2025-06-08 01:44:43 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-08 01:44:43 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-08 01:44:43 - ui.navigation - INFO - _load_module:292 - Module suivi_generator created and loaded successfully
2025-06-08 01:44:43 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: suivi_generator
2025-06-08 01:44:43 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-08 01:44:43 - ui.modules.suivi_generator_module - INFO - _restore_session:489 - Session restored successfully
2025-06-08 01:44:43 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-08 01:44:47 - ui.navigation - INFO - navigate_to:209 - Attempting to navigate to: home
2025-06-08 01:44:48 - ui.navigation - INFO - navigate_to:244 - Successfully navigated to: home
2025-06-08 01:44:50 - main - INFO - main:107 - Application closed normally
